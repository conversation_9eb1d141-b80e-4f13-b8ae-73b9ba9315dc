diff --git a/node_modules/react-native-webrtc/android/build/.transforms/11672af53d773d70460370572296f41b/results.bin b/node_modules/react-native-webrtc/android/build/.transforms/11672af53d773d70460370572296f41b/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/.transforms/11672af53d773d70460370572296f41b/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-webrtc/android/build/.transforms/11672af53d773d70460370572296f41b/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-webrtc/android/build/.transforms/11672af53d773d70460370572296f41b/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..5c8a2f5
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/.transforms/11672af53d773d70460370572296f41b/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/react-native-webrtc/android/build/.transforms/1ceb12c2fc23eb0b2ce5ffcb19f5e98c/results.bin b/node_modules/react-native-webrtc/android/build/.transforms/1ceb12c2fc23eb0b2ce5ffcb19f5e98c/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/.transforms/1ceb12c2fc23eb0b2ce5ffcb19f5e98c/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-webrtc/android/build/.transforms/1ceb12c2fc23eb0b2ce5ffcb19f5e98c/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-webrtc/android/build/.transforms/1ceb12c2fc23eb0b2ce5ffcb19f5e98c/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..29b765a
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/.transforms/1ceb12c2fc23eb0b2ce5ffcb19f5e98c/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/react-native-webrtc/android/build/.transforms/79a4e6b4c2d9fa16c4bc03f137e0d6cb/results.bin b/node_modules/react-native-webrtc/android/build/.transforms/79a4e6b4c2d9fa16c4bc03f137e0d6cb/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/.transforms/79a4e6b4c2d9fa16c4bc03f137e0d6cb/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-webrtc/android/build/.transforms/79a4e6b4c2d9fa16c4bc03f137e0d6cb/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-webrtc/android/build/.transforms/79a4e6b4c2d9fa16c4bc03f137e0d6cb/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..92e1553
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/.transforms/79a4e6b4c2d9fa16c4bc03f137e0d6cb/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/react-native-webrtc/android/build/.transforms/9ce5644ac74a5404e845e7b51a6a600f/results.bin b/node_modules/react-native-webrtc/android/build/.transforms/9ce5644ac74a5404e845e7b51a6a600f/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/.transforms/9ce5644ac74a5404e845e7b51a6a600f/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-webrtc/android/build/.transforms/9ce5644ac74a5404e845e7b51a6a600f/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-webrtc/android/build/.transforms/9ce5644ac74a5404e845e7b51a6a600f/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..8e04c37
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/.transforms/9ce5644ac74a5404e845e7b51a6a600f/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/react-native-webrtc/android/build/generated/source/buildConfig/debug/com/oney/WebRTCModule/BuildConfig.java b/node_modules/react-native-webrtc/android/build/generated/source/buildConfig/debug/com/oney/WebRTCModule/BuildConfig.java
new file mode 100644
index 0000000..0f35612
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/generated/source/buildConfig/debug/com/oney/WebRTCModule/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.oney.WebRTCModule;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.oney.WebRTCModule";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..dd02d0a
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,15 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="com.oney.WebRTCModule" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application>
+        <service
+            android:name="com.oney.WebRTCModule.MediaProjectionService"
+            android:foregroundServiceType="mediaProjection" >
+        </service>
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..5e59225
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.oney.WebRTCModule",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..dd02d0a
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,15 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="com.oney.WebRTCModule" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application>
+        <service
+            android:name="com.oney.WebRTCModule.MediaProjectionService"
+            android:foregroundServiceType="mediaProjection" >
+        </service>
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..5e59225
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.oney.WebRTCModule",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-webrtc/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/react-native-webrtc/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/react-native-webrtc/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/react-native-webrtc/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar b/node_modules/react-native-webrtc/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar
new file mode 100644
index 0000000..e6ffd02
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/react-native-webrtc/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..e6ffd02
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/react-native-webrtc/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..3c31630
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/react-native-webrtc/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..3c31630
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/react-native-webrtc/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..36951ef
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/compile_symbol_list/debug/R.txt
@@ -0,0 +1,4 @@
+int string media_projection_notification_text 0x0
+int string media_projection_notification_title 0x0
+int string ongoing_notification_channel_name 0x0
+int style AppTheme 0x0
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/react-native-webrtc/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..36951ef
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
@@ -0,0 +1,4 @@
+int string media_projection_notification_text 0x0
+int string media_projection_notification_title 0x0
+int string ongoing_notification_channel_name 0x0
+int style AppTheme 0x0
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..9ef199a
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Wed Mar 05 10:20:22 PST 2025
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml b/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
new file mode 100644
index 0000000..e1e31cd
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="media_projection_notification_text">You are currently sharing your screen.</string>
+    <string name="media_projection_notification_title">Screen sharing</string>
+    <string name="ongoing_notification_channel_name">Screen Sharing Notifications</string>
+    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        
+    </style>
+</resources>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..faec1d4
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/res"><file path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/res/values/styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        
+    </style></file><file path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/res/values/strings.xml" qualifiers=""><string name="media_projection_notification_title">Screen sharing</string><string name="media_projection_notification_text">You are currently sharing your screen.</string><string name="ongoing_notification_channel_name">Screen Sharing Notifications</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/react-native-webrtc/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..17902a4
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/react-native-webrtc/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..2878dbb
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/react-native-webrtc/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..4d95fa7
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/AbstractVideoCaptureController$CapturerEventsListener.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/AbstractVideoCaptureController$CapturerEventsListener.class
new file mode 100644
index 0000000..a90c7e3
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/AbstractVideoCaptureController$CapturerEventsListener.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/AbstractVideoCaptureController.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/AbstractVideoCaptureController.class
new file mode 100644
index 0000000..cf0664d
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/AbstractVideoCaptureController.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/BuildConfig.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/BuildConfig.class
new file mode 100644
index 0000000..0afd87e
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/BuildConfig.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController$1.class
new file mode 100644
index 0000000..dfa0e8b
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController$2.class
new file mode 100644
index 0000000..ec19829
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController.class
new file mode 100644
index 0000000..196b02c
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraCaptureController.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraEventsHandler.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraEventsHandler.class
new file mode 100644
index 0000000..0dae770
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/CameraEventsHandler.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DataChannelWrapper$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DataChannelWrapper$1.class
new file mode 100644
index 0000000..e5c8bba
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DataChannelWrapper$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DataChannelWrapper.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DataChannelWrapper.class
new file mode 100644
index 0000000..d9e26a0
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DataChannelWrapper.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DisplayUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DisplayUtils.class
new file mode 100644
index 0000000..56f06b6
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/DisplayUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/EglUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/EglUtils.class
new file mode 100644
index 0000000..7e7bb2c
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/EglUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$1.class
new file mode 100644
index 0000000..96fb6ca
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$2.class
new file mode 100644
index 0000000..d11da08
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$BiConsumer.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$BiConsumer.class
new file mode 100644
index 0000000..78dbf6f
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$BiConsumer.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$TrackPrivate.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$TrackPrivate.class
new file mode 100644
index 0000000..0a047e1
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl$TrackPrivate.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl.class
new file mode 100644
index 0000000..35bb328
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/GetUserMediaImpl.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/LibraryLoader.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/LibraryLoader.class
new file mode 100644
index 0000000..eb92071
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/LibraryLoader.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/MediaProjectionNotification.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/MediaProjectionNotification.class
new file mode 100644
index 0000000..00477ea
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/MediaProjectionNotification.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/MediaProjectionService.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/MediaProjectionService.class
new file mode 100644
index 0000000..0745509
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/MediaProjectionService.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/PeerConnectionObserver$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/PeerConnectionObserver$1.class
new file mode 100644
index 0000000..637c21f
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/PeerConnectionObserver$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/PeerConnectionObserver.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/PeerConnectionObserver.class
new file mode 100644
index 0000000..9bd84ed
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/PeerConnectionObserver.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/RTCVideoViewManager.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/RTCVideoViewManager.class
new file mode 100644
index 0000000..8b26d2d
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/RTCVideoViewManager.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ReactBridgeUtil$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ReactBridgeUtil$1.class
new file mode 100644
index 0000000..6227fae
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ReactBridgeUtil$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ReactBridgeUtil.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ReactBridgeUtil.class
new file mode 100644
index 0000000..7f70b24
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ReactBridgeUtil.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController$1.class
new file mode 100644
index 0000000..42cffab
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController$2.class
new file mode 100644
index 0000000..2cecff4
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController.class
new file mode 100644
index 0000000..6d9ba52
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ScreenCaptureController.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/SerializeUtils$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/SerializeUtils$1.class
new file mode 100644
index 0000000..09afe89
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/SerializeUtils$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/SerializeUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/SerializeUtils.class
new file mode 100644
index 0000000..c0c8991
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/SerializeUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/StringUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/StringUtils.class
new file mode 100644
index 0000000..9029e9a
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/StringUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ThreadUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ThreadUtils.class
new file mode 100644
index 0000000..5147018
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/ThreadUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/TrackCapturerEventsEmitter.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/TrackCapturerEventsEmitter.class
new file mode 100644
index 0000000..6e65fab
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/TrackCapturerEventsEmitter.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl$1.class
new file mode 100644
index 0000000..c8f9b27
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl.class
new file mode 100644
index 0000000..2853fb9
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter.class
new file mode 100644
index 0000000..85f519e
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/VideoTrackAdapter.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$1.class
new file mode 100644
index 0000000..bb996e6
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$2.class
new file mode 100644
index 0000000..20be665
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$3.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$3.class
new file mode 100644
index 0000000..18f9b7a
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$3.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$4.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$4.class
new file mode 100644
index 0000000..5aaa705
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$4.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$5.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$5.class
new file mode 100644
index 0000000..4446a3d
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$5.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$6.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$6.class
new file mode 100644
index 0000000..1eb6aea
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule$6.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule.class
new file mode 100644
index 0000000..e050d33
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModule.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModuleOptions.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModuleOptions.class
new file mode 100644
index 0000000..f635b17
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModuleOptions.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModulePackage.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModulePackage.class
new file mode 100644
index 0000000..6123b1a
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCModulePackage.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$1.class
new file mode 100644
index 0000000..7d93199
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$2.class
new file mode 100644
index 0000000..8af6baf
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$3.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$3.class
new file mode 100644
index 0000000..d9d2c83
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView$3.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView.class
new file mode 100644
index 0000000..bababc6
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/WebRTCView.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/ProcessorProvider.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/ProcessorProvider.class
new file mode 100644
index 0000000..f7fbf28
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/ProcessorProvider.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoEffectProcessor.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoEffectProcessor.class
new file mode 100644
index 0000000..3c0e926
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoEffectProcessor.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessor.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessor.class
new file mode 100644
index 0000000..dfdcdca
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessor.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessorFactoryInterface.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessorFactoryInterface.class
new file mode 100644
index 0000000..142567e
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessorFactoryInterface.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoDecoderFactory.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoDecoderFactory.class
new file mode 100644
index 0000000..888ac5b
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoDecoderFactory.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoEncoderFactory.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoEncoderFactory.class
new file mode 100644
index 0000000..fbb700d
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoEncoderFactory.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoDecoderFactoryProxy.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoDecoderFactoryProxy.class
new file mode 100644
index 0000000..ff2692e
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoDecoderFactoryProxy.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoEncoderFactoryProxy.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoEncoderFactoryProxy.class
new file mode 100644
index 0000000..8f05184
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoEncoderFactoryProxy.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/org/webrtc/Camera1Helper.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/org/webrtc/Camera1Helper.class
new file mode 100644
index 0000000..37ae748
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/org/webrtc/Camera1Helper.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/org/webrtc/Camera2Helper.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/org/webrtc/Camera2Helper.class
new file mode 100644
index 0000000..ee3ca53
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/classes/org/webrtc/Camera2Helper.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/AbstractVideoCaptureController$CapturerEventsListener.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/AbstractVideoCaptureController$CapturerEventsListener.class
new file mode 100644
index 0000000..a90c7e3
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/AbstractVideoCaptureController$CapturerEventsListener.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/AbstractVideoCaptureController.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/AbstractVideoCaptureController.class
new file mode 100644
index 0000000..cf0664d
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/AbstractVideoCaptureController.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/BuildConfig.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/BuildConfig.class
new file mode 100644
index 0000000..0afd87e
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/BuildConfig.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController$1.class
new file mode 100644
index 0000000..dfa0e8b
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController$2.class
new file mode 100644
index 0000000..ec19829
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController.class
new file mode 100644
index 0000000..196b02c
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraCaptureController.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraEventsHandler.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraEventsHandler.class
new file mode 100644
index 0000000..0dae770
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/CameraEventsHandler.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DataChannelWrapper$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DataChannelWrapper$1.class
new file mode 100644
index 0000000..e5c8bba
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DataChannelWrapper$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DataChannelWrapper.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DataChannelWrapper.class
new file mode 100644
index 0000000..d9e26a0
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DataChannelWrapper.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DisplayUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DisplayUtils.class
new file mode 100644
index 0000000..56f06b6
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/DisplayUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/EglUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/EglUtils.class
new file mode 100644
index 0000000..7e7bb2c
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/EglUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$1.class
new file mode 100644
index 0000000..96fb6ca
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$2.class
new file mode 100644
index 0000000..d11da08
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$BiConsumer.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$BiConsumer.class
new file mode 100644
index 0000000..78dbf6f
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$BiConsumer.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$TrackPrivate.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$TrackPrivate.class
new file mode 100644
index 0000000..0a047e1
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl$TrackPrivate.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl.class
new file mode 100644
index 0000000..35bb328
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/GetUserMediaImpl.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/LibraryLoader.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/LibraryLoader.class
new file mode 100644
index 0000000..eb92071
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/LibraryLoader.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/MediaProjectionNotification.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/MediaProjectionNotification.class
new file mode 100644
index 0000000..00477ea
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/MediaProjectionNotification.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/MediaProjectionService.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/MediaProjectionService.class
new file mode 100644
index 0000000..0745509
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/MediaProjectionService.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/PeerConnectionObserver$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/PeerConnectionObserver$1.class
new file mode 100644
index 0000000..637c21f
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/PeerConnectionObserver$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/PeerConnectionObserver.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/PeerConnectionObserver.class
new file mode 100644
index 0000000..9bd84ed
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/PeerConnectionObserver.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/RTCVideoViewManager.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/RTCVideoViewManager.class
new file mode 100644
index 0000000..8b26d2d
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/RTCVideoViewManager.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ReactBridgeUtil$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ReactBridgeUtil$1.class
new file mode 100644
index 0000000..6227fae
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ReactBridgeUtil$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ReactBridgeUtil.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ReactBridgeUtil.class
new file mode 100644
index 0000000..7f70b24
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ReactBridgeUtil.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController$1.class
new file mode 100644
index 0000000..42cffab
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController$2.class
new file mode 100644
index 0000000..2cecff4
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController.class
new file mode 100644
index 0000000..6d9ba52
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ScreenCaptureController.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/SerializeUtils$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/SerializeUtils$1.class
new file mode 100644
index 0000000..09afe89
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/SerializeUtils$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/SerializeUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/SerializeUtils.class
new file mode 100644
index 0000000..c0c8991
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/SerializeUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/StringUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/StringUtils.class
new file mode 100644
index 0000000..9029e9a
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/StringUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ThreadUtils.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ThreadUtils.class
new file mode 100644
index 0000000..5147018
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/ThreadUtils.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/TrackCapturerEventsEmitter.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/TrackCapturerEventsEmitter.class
new file mode 100644
index 0000000..6e65fab
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/TrackCapturerEventsEmitter.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl$1.class
new file mode 100644
index 0000000..c8f9b27
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl.class
new file mode 100644
index 0000000..2853fb9
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter$TrackMuteUnmuteImpl.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter.class
new file mode 100644
index 0000000..85f519e
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/VideoTrackAdapter.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$1.class
new file mode 100644
index 0000000..bb996e6
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$2.class
new file mode 100644
index 0000000..20be665
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$3.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$3.class
new file mode 100644
index 0000000..18f9b7a
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$3.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$4.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$4.class
new file mode 100644
index 0000000..5aaa705
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$4.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$5.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$5.class
new file mode 100644
index 0000000..4446a3d
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$5.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$6.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$6.class
new file mode 100644
index 0000000..1eb6aea
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule$6.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule.class
new file mode 100644
index 0000000..e050d33
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModule.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModuleOptions.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModuleOptions.class
new file mode 100644
index 0000000..f635b17
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModuleOptions.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModulePackage.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModulePackage.class
new file mode 100644
index 0000000..6123b1a
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCModulePackage.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$1.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$1.class
new file mode 100644
index 0000000..7d93199
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$1.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$2.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$2.class
new file mode 100644
index 0000000..8af6baf
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$2.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$3.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$3.class
new file mode 100644
index 0000000..d9d2c83
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView$3.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView.class
new file mode 100644
index 0000000..bababc6
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/WebRTCView.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/ProcessorProvider.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/ProcessorProvider.class
new file mode 100644
index 0000000..f7fbf28
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/ProcessorProvider.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoEffectProcessor.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoEffectProcessor.class
new file mode 100644
index 0000000..3c0e926
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoEffectProcessor.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessor.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessor.class
new file mode 100644
index 0000000..dfdcdca
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessor.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessorFactoryInterface.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessorFactoryInterface.class
new file mode 100644
index 0000000..142567e
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/videoEffects/VideoFrameProcessorFactoryInterface.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoDecoderFactory.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoDecoderFactory.class
new file mode 100644
index 0000000..888ac5b
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoDecoderFactory.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoEncoderFactory.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoEncoderFactory.class
new file mode 100644
index 0000000..fbb700d
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/H264AndSoftwareVideoEncoderFactory.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoDecoderFactoryProxy.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoDecoderFactoryProxy.class
new file mode 100644
index 0000000..ff2692e
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoDecoderFactoryProxy.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoEncoderFactoryProxy.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoEncoderFactoryProxy.class
new file mode 100644
index 0000000..8f05184
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/oney/WebRTCModule/webrtcutils/SoftwareVideoEncoderFactoryProxy.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/org/webrtc/Camera1Helper.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/org/webrtc/Camera1Helper.class
new file mode 100644
index 0000000..37ae748
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/org/webrtc/Camera1Helper.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/org/webrtc/Camera2Helper.class b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/org/webrtc/Camera2Helper.class
new file mode 100644
index 0000000..ee3ca53
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/org/webrtc/Camera2Helper.class differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/react-native-webrtc/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..15ff096
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,6 @@
+R_DEF: Internal format may change without notice
+local
+string media_projection_notification_text
+string media_projection_notification_title
+string ongoing_notification_channel_name
+style AppTheme
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/react-native-webrtc/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..15ff096
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,6 @@
+R_DEF: Internal format may change without notice
+local
+string media_projection_notification_text
+string media_projection_notification_title
+string ongoing_notification_channel_name
+style AppTheme
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-webrtc/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..e1026db
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,19 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    xmlns:tools="http://schemas.android.com/tools"
+4    package="com.oney.WebRTCModule" >
+5
+6    <uses-sdk android:minSdkVersion="24" />
+7
+8    <application>
+8-->/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:5:5-10:19
+9        <service
+9-->/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:6:9-9:19
+10            android:name="com.oney.WebRTCModule.MediaProjectionService"
+10-->/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:7:17-55
+11            android:foregroundServiceType="mediaProjection" >
+11-->/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:8:17-64
+12        </service>
+13    </application>
+14
+15</manifest>
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/react-native-webrtc/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..e1026db
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,19 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    xmlns:tools="http://schemas.android.com/tools"
+4    package="com.oney.WebRTCModule" >
+5
+6    <uses-sdk android:minSdkVersion="24" />
+7
+8    <application>
+8-->/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:5:5-10:19
+9        <service
+9-->/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:6:9-9:19
+10            android:name="com.oney.WebRTCModule.MediaProjectionService"
+10-->/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:7:17-55
+11            android:foregroundServiceType="mediaProjection" >
+11-->/Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:8:17-64
+12        </service>
+13    </application>
+14
+15</manifest>
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-webrtc/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..dd02d0a
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,15 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="com.oney.WebRTCModule" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application>
+        <service
+            android:name="com.oney.WebRTCModule.MediaProjectionService"
+            android:foregroundServiceType="mediaProjection" >
+        </service>
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/react-native-webrtc/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..dd02d0a
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,15 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    xmlns:tools="http://schemas.android.com/tools"
+    package="com.oney.WebRTCModule" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application>
+        <service
+            android:name="com.oney.WebRTCModule.MediaProjectionService"
+            android:foregroundServiceType="mediaProjection" >
+        </service>
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/react-native-webrtc/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-webrtc/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/react-native-webrtc/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml b/node_modules/react-native-webrtc/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml
new file mode 100644
index 0000000..e1e31cd
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="media_projection_notification_text">You are currently sharing your screen.</string>
+    <string name="media_projection_notification_title">Screen sharing</string>
+    <string name="ongoing_notification_channel_name">Screen Sharing Notifications</string>
+    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        
+    </style>
+</resources>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/packaged_res/debug/values/values.xml b/node_modules/react-native-webrtc/android/build/intermediates/packaged_res/debug/values/values.xml
new file mode 100644
index 0000000..e1e31cd
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/packaged_res/debug/values/values.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="media_projection_notification_text">You are currently sharing your screen.</string>
+    <string name="media_projection_notification_title">Screen sharing</string>
+    <string name="ongoing_notification_channel_name">Screen Sharing Notifications</string>
+    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        
+    </style>
+</resources>
\ No newline at end of file
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar b/node_modules/react-native-webrtc/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar
new file mode 100644
index 0000000..1144a2b
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/react-native-webrtc/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..1144a2b
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/react-native-webrtc/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..5edbdd8
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1,5 @@
+com.oney.WebRTCModule
+string media_projection_notification_text
+string media_projection_notification_title
+string ongoing_notification_channel_name
+style AppTheme
diff --git a/node_modules/react-native-webrtc/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/react-native-webrtc/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..5edbdd8
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1,5 @@
+com.oney.WebRTCModule
+string media_projection_notification_text
+string media_projection_notification_title
+string ongoing_notification_channel_name
+style AppTheme
diff --git a/node_modules/react-native-webrtc/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-webrtc/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..562beb9
--- /dev/null
+++ b/node_modules/react-native-webrtc/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,27 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:1:1-11:12
+INJECTED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:1:1-11:12
+	package
+		ADDED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:2:11-42
+		INJECTED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml
+	xmlns:tools
+		ADDED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:3:11-57
+	xmlns:android
+		ADDED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:1:11-69
+application
+ADDED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:5:5-10:19
+service#com.oney.WebRTCModule.MediaProjectionService
+ADDED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:6:9-9:19
+	android:foregroundServiceType
+		ADDED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:8:17-64
+	android:name
+		ADDED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml:7:17-55
+uses-sdk
+INJECTED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/dev/iDigestApp/node_modules/react-native-webrtc/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-webrtc/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-webrtc/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..5fc79cf
Binary files /dev/null and b/node_modules/react-native-webrtc/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/react-native-webrtc/android/src/main/java/com/oney/WebRTCModule/MediaProjectionService.java b/node_modules/react-native-webrtc/android/src/main/java/com/oney/WebRTCModule/MediaProjectionService.java
index c3cd9fe..5f93c14 100644
--- a/node_modules/react-native-webrtc/android/src/main/java/com/oney/WebRTCModule/MediaProjectionService.java
+++ b/node_modules/react-native-webrtc/android/src/main/java/com/oney/WebRTCModule/MediaProjectionService.java
@@ -73,11 +73,11 @@ public class MediaProjectionService extends Service {
 
         Notification notification = MediaProjectionNotification.buildMediaProjectionNotification(this);
 
-        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
-            startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION);
-        } else {
-            startForeground(NOTIFICATION_ID, notification);
-        }
+        //if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
+        //    startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION);
+        //} else {
+        //    startForeground(NOTIFICATION_ID, notification);
+        //}
 
         return START_NOT_STICKY;
     }

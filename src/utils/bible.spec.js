/* eslint-disable no-undef */

import { getBibleRef, initializeAsNeeded } from '@/utils/bible';

import { expect } from '@jest/globals';

describe('Bible', () => {
  beforeAll(() => {
    initializeAsNeeded(require('@/assets/json/bible.json'));
  });

  it('getBibleRef', async () => {
    let result;

    result = getBibleRef('约翰福音3:19-21,8:12,12:46');
    expect(result).toMatchObject([
      { type: 'bible', value: '约翰福音3:19-21', book: '约翰福音', verse: '3:19-21' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '8:12', book: '约翰福音', verse: '8:12' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '12:46', book: '约翰福音', verse: '12:46' }
    ]);

    result = getBibleRef('约翰福音3:19-21,8:12,14,16-19');
    expect(result).toMatchObject([
      { type: 'bible', value: '约翰福音3:19-21', book: '约翰福音', verse: '3:19-21' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '8:12', book: '约翰福音', verse: '8:12' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '14', book: '约翰福音', verse: '8:14' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '16-19', book: '约翰福音', verse: '8:16-19' }
    ]);

    // chinese chapter & verse names
    result = getBibleRef('第5天：阅读以赛亚书48章\n尽管以色列叛逆，上帝仍是信实的。');
    expect(result).toMatchObject([
      { type: 'text', value: '第5天：阅读' },
      { type: 'bible', value: '以赛亚书48章', book: '以赛亚书', verse: '48' },
      { type: 'text', value: '尽管以色列叛逆，上帝仍是信实的。' }
    ]);

    result = getBibleRef('第6天：复习以赛亚书40-48章 上帝值得信赖，祂必拯救。');
    expect(result).toMatchObject([
      { type: 'text', value: '第6天：复习' },
      { type: 'bible', value: '以赛亚书40-48章', book: '以赛亚书', verse: '40-48' },
      { type: 'text', value: '上帝值得信赖，祂必拯救。' }
    ]);
    result = getBibleRef('创世记1章29');
    expect(result).toMatchObject([{ type: 'bible', value: '创世记1章29', book: '创世记', verse: '1:29' }]);
    result = getBibleRef('创世记1章29节');
    expect(result).toMatchObject([{ type: 'bible', value: '创世记1章29节', book: '创世记', verse: '1:29' }]);
    result = getBibleRef('创世记1章29-31');
    expect(result).toMatchObject([{ type: 'bible', value: '创世记1章29-31', book: '创世记', verse: '1:29-31' }]);
    result = getBibleRef('创世记1-2章');
    expect(result).toMatchObject([{ type: 'bible', value: '创世记1-2章', book: '创世记', verse: '1-2' }]);
    result = getBibleRef('创世记1章29-31节');
    expect(result).toMatchObject([{ type: 'bible', value: '创世记1章29-31节', book: '创世记', verse: '1:29-31' }]);
    result = getBibleRef('创世记1章29-2章12');
    expect(result).toMatchObject([{ type: 'bible', value: '创世记1章29-2章12', book: '创世记', verse: '1:29-2:12' }]);
    result = getBibleRef('创世记1章29-2章12节');
    expect(result).toMatchObject([{ type: 'bible', value: '创世记1章29-2章12节', book: '创世记', verse: '1:29-2:12' }]);

    // one chapter
    result = getBibleRef('John 1');
    expect(result).toMatchObject([{ type: 'bible', value: 'John 1', book: 'John', verse: '1' }]);

    // one verse
    result = getBibleRef('John 2:1');
    expect(result).toMatchObject([{ type: 'bible', value: 'John 2:1', book: 'John', verse: '2:1' }]);

    // one chapter multiple verses
    result = getBibleRef('John 2:2-4');
    expect(result).toMatchObject([{ type: 'bible', value: 'John 2:2-4', book: 'John', verse: '2:2-4' }]);

    // multiple chapters
    result = getBibleRef('John 2-4');
    expect(result).toMatchObject([{ type: 'bible', value: 'John 2-4', book: 'John', verse: '2-4' }]);

    // multiple chapters multiple verses
    result = getBibleRef('John 2:1-3:14');
    expect(result).toMatchObject([{ type: 'bible', value: 'John 2:1-3:14', book: 'John', verse: '2:1-3:14' }]);

    // 以赛亚书1:1 (not 书1:1)
    result = getBibleRef('以赛亚书1:1');
    expect(result).toMatchObject([{ type: 'bible', value: '以赛亚书1:1', book: '以赛亚书', verse: '1:1' }]);

    // not bible verse
    result = getBibleRef('以赛亚 ABC 123');
    expect(result).toMatchObject([{ type: 'text', value: '以赛亚 ABC 123' }]);

    // continues verse 1
    result = getBibleRef('John 1:1; 2:3-4');
    expect(result).toMatchObject([
      { type: 'bible', value: 'John 1:1', book: 'John', verse: '1:1' },
      { type: 'text', value: ';' },
      { type: 'bible', value: '2:3-4', book: 'John', verse: '2:3-4' }
    ]);

    // continues verse 2
    result = getBibleRef('John 1:1, 6, 7, 8-9');
    expect(result).toMatchObject([
      { type: 'bible', value: 'John 1:1', book: 'John', verse: '1:1' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '6', book: 'John', verse: '1:6' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '7', book: 'John', verse: '1:7' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '8-9', book: 'John', verse: '1:8-9' }
    ]);

    // continues verse 3
    result = getBibleRef('6.a 耶利米预告了哪些关于上帝对犹大国审判的内容? (参阅耶利米书4:6;13:18-20;20:4-6;25:8-14。)');
    expect(result).toMatchObject([
      { type: 'text', value: '6.a 耶利米预告了哪些关于上帝对犹大国审判的内容? (参阅' },
      { book: '耶利米书', type: 'bible', value: '耶利米书4:6', verse: '4:6' },
      {
        type: 'text',
        value: ';'
      },
      {
        book: '耶利米书',
        type: 'bible',
        value: '13:18-20',
        verse: '13:18-20'
      },
      {
        type: 'text',
        value: ';'
      },
      {
        book: '耶利米书',
        type: 'bible',
        value: '20:4-6',
        verse: '20:4-6'
      },
      {
        type: 'text',
        value: ';'
      },
      {
        book: '耶利米书',
        type: 'bible',
        value: '25:8-14',
        verse: '25:8-14'
      },
      {
        type: 'text',
        value: '。)'
      }
    ]);

    result = getBibleRef('约珥书3:1-2,9-21');
    expect(result).toMatchObject([
      { type: 'bible', value: '约珥书3:1-2', book: '约珥书', verse: '3:1-2' },
      { type: 'text', value: ',' },
      { type: 'bible', value: '9-21', book: '约珥书', verse: '3:9-21' }
    ]);

    // test with invalid chapter
    result = getBibleRef('创世记51');
    expect(result).toMatchObject([{ type: 'text', value: '创世记51' }]);
    result = getBibleRef('创世记50');
    expect(result).toMatchObject([{ type: 'bible', value: '创世记50', book: '创世记', verse: '50' }]);

    // test with abbreviation chars
    result = getBibleRef('创12:1-2');
    expect(result).toMatchObject([{ type: 'bible', value: '创12:1-2', book: '创', verse: '12:1-2' }]);

    // test with 'Psalm' (without s)
    result = getBibleRef('Psalm12:1-2');
    expect(result).toMatchObject([{ type: 'bible', value: 'Psalm12:1-2', book: 'Psalm', verse: '12:1-2' }]);

    // test with en dash
    result = getBibleRef('From Ezekiel 2:9–3:2, what might “eating” the scroll signify?');
    expect(result).toMatchObject([
      { type: 'text', value: 'From ' },
      { type: 'bible', value: 'Ezekiel 2:9-3:2', book: 'Ezekiel', verse: '2:9-3:2' },
      { type: 'text', value: ', what might “eating” the scroll signify?' }
    ]);
  });
});

import {
  downloadAsync,
  getFileInfoAsync,
  getObjectAsync,
  getStringAsync,
  setObjectAsync
} from '@/dataStorage/localStorage';
import { getCurrentUser } from '@/utils/user';
import { BibleJsonFormat, ETranslation, IChapter, IVerse } from './types';

/**
 * Download Bible data from the server
 * @param translation The translation to download
 * @returns Whether the download was successful
 */
export const downloadBibleAsync = async (translation: ETranslation) => {
  console.log('downloadBibleAsync:' + translation);
  try {
    const urlObj = new URL(getCurrentUser().getHttpsServer());
    //the Bible domain is the last two part of the domain
    urlObj.hostname = urlObj.hostname.split('.').slice(-2).join('.');
    urlObj.pathname = '/bible/' + translation + '.json';
    const downloadBibleUrl = urlObj.href;
    return await downloadAsync(`book-${translation}`, downloadBibleUrl);
  } catch (error) {
    try {
      //try one more time with the cryptoproxy url
      const urlObj = new URL(getCurrentUser().getCryptoProxy());
      urlObj.pathname = '/bible/' + translation + '.json';
      const downloadBibleUrl = urlObj.href;
      return await downloadAsync(`book-${translation}`, downloadBibleUrl);
    } catch (error) {
      alert(error);
      console.log(error);
    }
  }
  return false;
};

/**
 * Check if a Bible version is downloaded
 * @param translation The translation to check
 * @returns Whether the translation is downloaded
 */
export const isBibleVersionDownloaded = async (translation: ETranslation): Promise<boolean> => {
  const downloadStatus = ((await getObjectAsync('bible.downloads')) as Record<string, boolean>) || {};
  return !!downloadStatus[translation];
};

/**
 * Check Bible data integrity
 * @param translation The translation to check
 * @returns Whether the data is intact
 */
export const checkBibleDataIntegrity = async (translation: ETranslation): Promise<boolean> => {
  try {
    // Check if marked as downloaded
    const isMarkedAsDownloaded = await isBibleVersionDownloaded(translation);
    if (!isMarkedAsDownloaded) {
      return false;
    }

    // Check if all 66 books have data
    for (let bookId = 1; bookId <= 66; bookId++) {
      const storageKey = `bible.chapters.${bookId}.${translation}`;
      const bookData = await getObjectAsync(storageKey);

      // If book data is missing or empty, data is incomplete
      if (!bookData || Object.keys(bookData).length === 0) {
        console.log(`Book ${bookId} is missing for translation ${translation}`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error(`Error checking Bible data integrity for ${translation}:`, error);
    return false;
  }
};

/**
 * Repair Bible data by redownloading
 * @param translation The translation to repair
 * @returns Whether the repair was successful
 */
export const repairBibleData = async (translation: ETranslation): Promise<boolean> => {
  try {
    console.log(`Repairing Bible data for translation: ${translation}`);
    // Redownload and store Bible data
    return await downloadAndStoreBible(translation);
  } catch (error) {
    console.error(`Failed to repair Bible data for ${translation}:`, error);
    return false;
  }
};

/**
 * Set version download status
 * @param translation The translation
 * @param isDownloaded Whether it's downloaded
 */
export const setVersionDownloaded = async (translation: ETranslation, isDownloaded: boolean): Promise<void> => {
  const downloadStatus = ((await getObjectAsync('bible.downloads')) as Record<string, boolean>) || {};
  downloadStatus[translation] = isDownloaded;
  await setObjectAsync('bible.downloads', downloadStatus);
};

/**
 * Convert Bible JSON to app format
 * @param bibleJson The Bible JSON
 * @returns Formatted Bible data
 */
export const convertBibleJsonToAppFormat = (bibleJson: BibleJsonFormat): Record<number, IChapter> => {
  const bookChapters: Record<number, IChapter> = {};

  // Process all verse IDs
  Object.entries(bibleJson).forEach(([verseIdStr, content]) => {
    const verseId = parseInt(verseIdStr);

    // Parse verseId to get bookId, chapter, verse
    // verseId format: bookId(1-2 digits) + chapter(3 digits) + verse(3 digits)
    const verseIdString = verseId.toString();
    const bookId = parseInt(verseIdString.slice(0, -6));
    const chapter = parseInt(verseIdString.slice(-6, -3));
    const verse = parseInt(verseIdString.slice(-3));

    // Initialize bookId data structure
    if (!bookChapters[bookId]) {
      bookChapters[bookId] = {};
    }

    // Ensure bookChapters[bookId] exists
    const bookData = bookChapters[bookId];
    if (bookData) {
      // Initialize chapter data structure
      if (!bookData[chapter]) {
        bookData[chapter] = [];
      }

      // Add verse data
      const verseData: IVerse = {
        verse,
        verseId,
        content,
        paraStart: 0 // Default value
      };

      // Ensure bookData[chapter] exists
      const chapterData = bookData[chapter];
      if (chapterData) {
        chapterData.push(verseData);
      }
    }
  });

  // Sort verses in each chapter
  Object.keys(bookChapters).forEach((bookIdStr) => {
    const bookId = parseInt(bookIdStr);
    const bookData = bookChapters[bookId];

    if (bookData) {
      // Use type assertion to ensure TypeScript knows this is an object
      const chapters = Object.keys(bookData as Record<string, IVerse[]>);

      chapters.forEach((chapterStr) => {
        const chapter = parseInt(chapterStr);
        const chapterData = bookData[chapter];

        if (chapterData) {
          chapterData.sort((a, b) => {
            if (typeof a.verse === 'number' && typeof b.verse === 'number') {
              return a.verse - b.verse;
            }
            return 0;
          });
        }
      });
    }
  });

  return bookChapters;
};

/**
 * Download and store Bible data
 * @param translation The translation to download
 * @returns Whether the operation was successful
 */
export const downloadAndStoreBible = async (translation: ETranslation): Promise<boolean> => {
  try {
    console.log(`Downloading Bible translation: ${translation}`);

    // Download the Bible file
    await downloadBibleAsync(translation);
    const info = await getFileInfoAsync(`book-${translation}`);
    if (!info || !info.exists) {
      return false;
    }

    const content = await getStringAsync(`book-${translation}`);
    if (!content) {
      return false;
    }

    const bibleJson: BibleJsonFormat = await JSON.parse(content);

    // TODO: Consider moving this conversion to server-side to reduce client processing
    // The server could provide pre-formatted data in the app's expected structure
    // instead of requiring client-side conversion from the raw JSON format
    // Convert to app format
    const bookChapters = convertBibleJsonToAppFormat(bibleJson);
    // Store each book's data
    for (const bookId in bookChapters) {
      const bookData = bookChapters[parseInt(bookId)];
      const storageKey = `bible.chapters.${bookId}.${translation}`;
      await setObjectAsync(storageKey, bookData);
    }

    // Update download status
    await setVersionDownloaded(translation, true);

    return true;
  } catch (error) {
    console.error(`Failed to download Bible translation ${translation}:`, error);
    return false;
  }
};

import { StyleSheet, Text, TouchableHighlight, View, Image, TouchableOpacity } from 'react-native';

import React from 'react';
import { VerseItemProps } from './types';
import { getFontSize } from '@/utils/getFontSize';

export default function VerseItem({
  verse,
  chapterNumber,
  highlighted,
  selected,
  showCheckbox,
  noteCount,
  onPress,
  onLongPress,
  onNotePress
}: VerseItemProps) {
  return (
    <TouchableHighlight
      style={styles.verseContainer}
      underlayColor='#DDDDDD'
      activeOpacity={highlighted ? 1 : 0.7}
      onPress={onPress}
      onLongPress={onLongPress}>
      <>
        <View style={styles.verseHeader}>
          <View style={styles.verseHeaderLeft}>
            <Text
              style={[
                styles.verseId,
                { fontSize: getFontSize().smallFontSize, lineHeight: 20, paddingHorizontal: 4 },
                highlighted && { backgroundColor: '#fffa99', borderRadius: 4 }
              ]}>{`${chapterNumber}:${verse.verse}`}</Text>
            {/* 显示笔记信息 */}
            {noteCount > 0 && (
              <TouchableOpacity style={styles.noteButton} onPress={onNotePress}>
                <Image
                  source={require('@/assets/images/bible/icon-Notes2.png')}
                  style={styles.noteIcon}
                />
                <Text style={styles.noteText}>笔记</Text>
                <View style={styles.noteCountBadge}>
                  <Text style={styles.noteCountText}>{noteCount}</Text>
                </View>
              </TouchableOpacity>
            )}
          </View>
          {/* 操作栏出现后所有verse都显示checkbox */}
          {showCheckbox && (
            <View style={styles.checkboxBox}>{selected && <Text style={styles.checkboxCheck}>✓</Text>}</View>
          )}
        </View>
        {/* Display verse number and content from all translations */}
        <View style={[styles.verseContentContainer, highlighted && { backgroundColor: '#fffa99', borderRadius: 4 }]}>
          {verse.translations &&
            Object.entries(verse.translations).map(([translation, content]) => {
              return (
                <View key={translation} style={styles.translationContent}>
                  <Text
                    style={[
                      styles.verseContent,
                      { fontSize: getFontSize().mediumFontSize, lineHeight: getFontSize().mediumFontSize + 4 }
                    ]}>
                    {content}
                  </Text>
                </View>
              );
            })}
        </View>
        {verse.paraStart === 1 && <View style={styles.paragraphIndicator} />}
      </>
    </TouchableHighlight>
  );
}

const styles = StyleSheet.create({
  verseContainer: { marginBottom: 10 },
  verseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  verseHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  verseId: { color: '#666', fontWeight: 'bold', lineHeight: 22, height: 22 },
  noteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
    paddingHorizontal: 2,
    paddingVertical: 2,
    borderRadius: 12,
    borderWidth: 0.5,
    borderColor: '#acacac'
  },
  noteIcon: {
    width: 12,
    height: 12,
    marginRight: 2,
    tintColor: '#fff',
    backgroundColor: '#333',
    borderRadius: 6,
  },
  noteText: {
    fontSize: 10,
    color: '#000',
    fontWeight: 'bold'
  },
  noteCountBadge: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4
  },
  noteCountText: {
    color: '#000',
    fontSize: 10,
    fontWeight: 'bold'
  },
  verseContentContainer: { paddingHorizontal: 4 },
  translationContent: { marginBottom: 4 },
  verseContent: { color: '#333' },
  paragraphIndicator: { height: 10 },
  checkboxBox: {
    width: 18,
    height: 18,
    marginHorizontal: 4,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: '#000',
    borderRadius: 2,
    backgroundColor: '#fff'
  },
  checkboxCheck: {
    color: '#333',
    fontSize: 16,
    fontWeight: 'bold'
  }
});

import { StyleSheet, View } from 'react-native';

import { ChapterContainerProps } from './types';
import React from 'react';
import VerseItem from './VerseItem';

export default function ChapterContainer({
  chapter,
  highlightedVerseIds,
  selectedVerseIds,
  actionBarVisible,
  notesData,
  onVersePress,
  onVerseLongPress,
  onNotePress,
  onLayout
}: ChapterContainerProps) {
  return (
    <View style={styles.chapterContainer} onLayout={onLayout}>
      {chapter.verses.map((verse) => {
        // 获取当前verse的笔记数量
        const verseNotes = notesData[verse.verseId];
        const noteCount = verseNotes && Array.isArray(verseNotes) ? verseNotes.length :
                         (verseNotes ? 1 : 0); // 兼容旧格式

        return (
          <VerseItem
            key={verse.verseId}
            verse={verse}
            chapterNumber={chapter.chapter}
            highlighted={highlightedVerseIds.includes(verse.verseId)}
            selected={selectedVerseIds.includes(verse.verseId)}
            showCheckbox={actionBarVisible}
            noteCount={noteCount}
            onPress={() => onVersePress(verse.verseId)}
            onLongPress={() => onVerseLongPress(verse, chapter.chapter)}
            onNotePress={() => onNotePress(verse.verseId, chapter.chapter, verse.verse)}
          />
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  chapterContainer: { paddingVertical: 15 }
});

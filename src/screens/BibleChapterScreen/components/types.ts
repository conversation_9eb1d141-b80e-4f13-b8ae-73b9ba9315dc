import { Animated } from 'react-native';
import { ETranslation } from '@/utils/bibleUtils/types';

// 基础类型定义
export interface IVerse {
  verseId: number;
  verse: string | number;
  content: string;
  translations?: Record<string, string>;
  paraStart: number;
}

export interface ChapterData {
  chapter: number;
  verses: IVerse[];
}

// 事件类型定义
export interface ScrollEvent {
  nativeEvent: {
    contentOffset: {
      x: number;
      y: number;
    };
  };
}

export interface LayoutEvent {
  nativeEvent: {
    layout: {
      height: number;
      width: number;
      x: number;
      y: number;
    };
  };
}

export interface ScrollToIndexFailedInfo {
  index: number;
  highestMeasuredFrameIndex: number;
  averageItemLength: number;
}

// VerseItem 组件类型
export interface VerseItemProps {
  verse: IVerse;
  chapterNumber: number;
  highlighted: boolean;
  selected: boolean;
  showCheckbox: boolean;
  noteCount: number;
  onPress: () => void;
  onLongPress: () => void;
  onNotePress: () => void;
}

// ChapterContainer 组件类型
export interface ChapterContainerProps {
  chapter: ChapterData;
  highlightedVerseIds: number[];
  selectedVerseIds: number[];
  actionBarVisible: boolean;
  notesData: Record<number, any>;
  onVersePress: (verseId: number) => void;
  onVerseLongPress: (verse: IVerse, chapter: number) => void;
  onNotePress: (verseId: number, chapterNumber: number, verseNumber: number | string) => void;
  onLayout?: (event: LayoutEvent) => void;
}

// ChapterList 组件类型
export interface ChapterListProps {
  chaptersData: ChapterData[];
  highlightedVerseIds: number[];
  selectedVerseIds: number[];
  actionBarVisible: boolean;
  onVersePress: (verseId: number) => void;
  onVerseLongPress: (verse: IVerse, chapter: number) => void;
  onScroll: (event: ScrollEvent) => void;
  onEndReached: () => void;
  onStartReached: (info: { distanceFromStart: number }) => void;
  onScrollToIndexFailed?: (info: ScrollToIndexFailedInfo) => void;
  contentContainerStyle?: object;
  initialNumToRender?: number;
  onLayout?: (event: LayoutEvent) => void;
}

// ChapterSelector 组件类型
export interface ChapterSelectorProps {
  chapterNumbers: number[];
  currentChapter: number;
  verseCircleSize: number;
  onChapterSelect: (chapter: number) => void;
  onScroll?: (event: ScrollEvent) => void;
}

// BibleTools 组件类型
export interface BibleToolsProps {
  visible: boolean;
  anim: Animated.Value;
  selectedVerseIds: number[];
  highlightedVerseIds: number[];
  chaptersData: ChapterData[];
  currentTranslation?: ETranslation[];
  onHighlight: (verseIds: number[]) => void;
  onUnhighlight: (verseIds: number[]) => void;
  onCopy: (verseIds: number[]) => void;
  onNote?: (verseIds: number[], bookName: string, verseRange: string) => void;
  onCancel: () => void;
}

// 操作栏状态类型
export interface ActionBarState {
  visible: boolean;
  selectedVerseIds: number[];
  highlightedVerseIds: number[];
}

// 章节选择器状态类型
export interface ChapterSelectorState {
  currentChapter: number;
  chapterNumbers: number[];
  verseCircleSize: number;
}

// 经文操作回调类型
export interface VerseActionCallbacks {
  onVersePress: (verseId: number) => void;
  onVerseLongPress: (verse: IVerse, chapter: number) => void;
  onVerseHighlight: (verseId: number) => void;
  onVerseUnhighlight: (verseId: number) => void;
  onVerseCopy: (verseIds: number[]) => void;
}

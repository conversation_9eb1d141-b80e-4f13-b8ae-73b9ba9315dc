import { ChapterData, ChapterListProps } from './types';
import React, { forwardRef } from 'react';

import ChapterContainer from './ChapterContainer';
import { FlatList } from 'react-native';

const ChapterList = forwardRef<FlatList<ChapterData>, ChapterListProps>(
  (
    {
      chaptersData,
      highlightedVerseIds,
      selectedVerseIds,
      actionBarVisible,
      onVersePress,
      onVerseLongPress,
      onScroll,
      onEndReached,
      onStartReached,
      onScrollToIndexFailed,
      contentContainerStyle,
      initialNumToRender,
      onLayout
    },
    ref
  ) => {
    return (
      <FlatList
        ref={ref}
        data={chaptersData}
        initialNumToRender={initialNumToRender || chaptersData.length}
        keyExtractor={(item) => item.chapter.toString()}
        onScroll={onScroll}
        onEndReached={onEndReached}
        onStartReached={onStartReached}
        onScrollToIndexFailed={onScrollToIndexFailed}
        contentContainerStyle={contentContainerStyle}
        renderItem={({ item }) => (
          <ChapterContainer
            key={item.chapter}
            chapter={item}
            highlightedVerseIds={highlightedVerseIds}
            selectedVerseIds={selectedVerseIds}
            actionBarVisible={actionBarVisible}
            onVersePress={onVersePress}
            onVerseLongPress={onVerseLongPress}
            {...(onLayout && { onLayout })}
          />
        )}
      />
    );
  }
);

ChapterList.displayName = 'ChapterList';

export default ChapterList;

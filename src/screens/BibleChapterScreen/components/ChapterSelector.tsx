import React, { forwardRef } from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity } from 'react-native';

import { ChapterSelectorProps } from './types';
import { getFontSize } from '@/utils/getFontSize';

const ChapterSelector = forwardRef<ScrollView, ChapterSelectorProps>(
  ({ chapterNumbers, currentChapter, verseCircleSize, onChapterSelect, onScroll }, ref) => {
    ChapterSelector.displayName = 'ChapterSelector';
    return (
      <ScrollView
        ref={ref}
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.chapterSelector}
        onScroll={onScroll}
        scrollEventThrottle={16}>
        {chapterNumbers.map((num) => (
          <TouchableOpacity
            key={num}
            style={[
              styles.chapterButton,
              currentChapter === num && styles.activeChapterButton,
              {
                width: verseCircleSize,
                height: verseCircleSize,
                borderRadius: verseCircleSize / 2,
                margin: 2.5
              }
            ]}
            onPress={() => onChapterSelect(num)}>
            <Text
              style={[
                styles.chapterButtonText,
                currentChapter === num && styles.activeChapterButtonText,
                { fontSize: getFontSize().mediumFontSize }
              ]}>
              {num}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  }
);

export default ChapterSelector;

const styles = StyleSheet.create({
  chapterSelector: {
    position: 'absolute',
    top: 0,
    width: '100%',
    zIndex: 2
  },
  chapterButton: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#fff'
  },
  chapterButtonText: { color: '#333' },
  activeChapterButton: { backgroundColor: '#333' },
  activeChapterButtonText: { color: '#fff' }
});

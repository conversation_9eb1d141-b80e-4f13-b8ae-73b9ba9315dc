import * as Clipboard from 'expo-clipboard';

import { Animated, Image, SafeAreaView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

import { BibleToolsProps } from './types';
import { ETranslation } from '@/utils/bibleUtils/types';
import React from 'react';
import { getFontSize } from '@/utils/getFontSize';
import { getVerseInfoByVerseId } from '@/utils/bibleUtils/utils';

// 工具函数：格式化经节范围
function formatVerseRanges(verseInfos: { chapter: number; verse: number }[]) {
  // 过滤掉 undefined/null
  const filtered = verseInfos.filter((v): v is { chapter: number; verse: number } => !!v && v.chapter !== undefined && v.verse !== undefined);
  if (filtered.length === 0) {
    return '';
  }
  // 按章节、经节排序
  filtered.sort((a, b) => a.chapter === b.chapter ? a.verse - b.verse : a.chapter - b.chapter);
  const result: string[] = [];
  if (filtered?.length > 0) {
    let lastChapter = filtered[0]?.chapter;
    let rangeStart = filtered[0]?.verse;
    let rangeEnd = filtered[0]?.verse || 0;
    let chapterStr = `${lastChapter}:`;
    for (let i = 1; i < filtered.length; i++) {
      const { chapter, verse = 0 } = filtered[i] || {};
      if (chapter === lastChapter && verse === rangeEnd + 1) {
        // 连续
        rangeEnd = verse;
      } else {
        // 断开，输出上一个区间
        chapterStr += (rangeStart === rangeEnd ? `${rangeStart}` : `${rangeStart}-${rangeEnd}`) + ',';
        if (chapter !== lastChapter) {
          result.push(chapterStr.replace(/,$/, ''));
          chapterStr = `${chapter}:`;
        }
        rangeStart = rangeEnd = verse;
        lastChapter = chapter;
      }
    }
    // 输出最后一个区间
    chapterStr += (rangeStart === rangeEnd ? `${rangeStart}` : `${rangeStart}-${rangeEnd}`);
    result.push(chapterStr.replace(/,$/, ''));
  }

  return result.join(';');
}

interface BibleToolsPropsEx extends BibleToolsProps {
  bookName: string;
  currentTranslation?: ETranslation[];
  onNote?: (verseIds: number[], bookName: string, verseRange: string) => void;
}

export default function BibleTools({
  visible,
  anim,
  selectedVerseIds,
  highlightedVerseIds,
  chaptersData,
  onHighlight,
  onUnhighlight,
  onCopy,
  onNote,
  onCancel,
  bookName,
  currentTranslation
}: BibleToolsPropsEx) {
  if (!visible) {
    return null;
  }

  // 检查选中的verses中是否有已经高亮的
  const hasHighlighted = selectedVerseIds.some((verseId) => highlightedVerseIds.includes(verseId));

  const handleHighlight = () => {
    // 对所有选中的verses进行高亮操作
    selectedVerseIds.forEach((verseId) => {
      if (!highlightedVerseIds.includes(verseId)) {
        onHighlight([verseId]);
      }
    });
  };

  const handleUnhighlight = () => {
    // 对所有选中的verses取消高亮
    selectedVerseIds.forEach((verseId) => {
      if (highlightedVerseIds.includes(verseId)) {
        onUnhighlight([verseId]);
      }
    });
  };

  const handleCopy = () => {
    if (selectedVerseIds.length > 0 && currentTranslation && currentTranslation.length > 0) {
      // 获取第一个翻译版本作为基础版本
      const baseTranslation = currentTranslation[0];
      
      // 类型检查确保 baseTranslation 不为 undefined
      if (!baseTranslation) {
        return;
      }
      
      // 获取翻译版本名称的函数
      const getTranslationName = (translation: ETranslation): string => {
        switch (translation) {
          case ETranslation.cuvs:
            return '和合本';
          case ETranslation.cuvt:
            return '和合本修订版';
          case ETranslation.asv:
            return 'ASV';
          default:
            return '未知版本';
        }
      };
      
      // 获取所有选中 verse 的 verseInfo
      const verseInfos = selectedVerseIds.map((verseId) => getVerseInfoByVerseId(verseId, baseTranslation));
      // 格式化章节:经节
      const verseRangeStr = formatVerseRanges(verseInfos);
      // 获取所有选中 verse 的内容
      const selectedVerses = chaptersData.flatMap((chapter) =>
        chapter.verses.filter((verse) => selectedVerseIds.includes(verse.verseId))
      );
      
      // 如果有多个翻译版本，则复制所有版本的内容
      if (currentTranslation.length > 1) {
        const allTranslationsText = currentTranslation.map((translation) => {
          const translationName = getTranslationName(translation);
          const translationText = selectedVerses
            .map((verse) => {
              const verseText = verse.translations && verse.translations[translation]
                ? verse.translations[translation]
                : verse.content;
              return verseText;
            })
            .join('\n');
          return `${translationName}：\n${translationText}`;
        }).join('\n\n');
        
        const finalText = `${allTranslationsText}\n（${bookName} ${verseRangeStr}）`;
        Clipboard.setStringAsync(finalText);
      } else {
        // 单个版本的情况
        const translationName = getTranslationName(baseTranslation);
        const text = selectedVerses
          .map((verse) => {
            const verseText = verse.translations && verse.translations[baseTranslation]
              ? verse.translations[baseTranslation]
              : verse.content;
            return verseText;
          })
          .join('\n');
        // 拼接最终内容
        const finalText = `${text}\n（${translationName} ${bookName} ${verseRangeStr}）`;
        Clipboard.setStringAsync(finalText);
      }
    }
    onCopy(selectedVerseIds);
  };

  const handleNote = () => {
    if (onNote && selectedVerseIds.length > 0 && currentTranslation && currentTranslation.length > 0) {
      // 获取第一个翻译版本作为基础版本
      const baseTranslation = currentTranslation[0];

      // 类型检查确保 baseTranslation 不为 undefined
      if (!baseTranslation) {
        return;
      }

      // 获取所有选中 verse 的 verseInfo
      const verseInfos = selectedVerseIds.map((verseId) => getVerseInfoByVerseId(verseId, baseTranslation));

      // 格式化章节:经节
      const verseRangeStr = formatVerseRanges(verseInfos);

      // 调用onNote，传递verseIds、bookName和verseRange
      onNote(selectedVerseIds, bookName, verseRangeStr);
    }
  };

  return (
    <Animated.View
      style={[
        styles.actionBar,
        {
          transform: [
            {
              translateY: anim.interpolate({
                inputRange: [0, 1],
                outputRange: [120, 0]
              })
            }
          ],
          opacity: anim
        }
      ]}>
      <SafeAreaView>
        <View style={styles.actionBarContent}>
          {hasHighlighted ? (
            // 如果有高亮的，显示取消高亮按钮
            <TouchableOpacity style={styles.actionBarBtn} onPress={handleUnhighlight}>
              <Image
                source={require('@/assets/images/bible/icon-unHighlight.png')}
                style={{ width: 28, height: 28, marginBottom: 2 }}
              />
              <Text style={styles.actionBarBtnText}>取消高亮</Text>
            </TouchableOpacity>
          ) : (
            // 如果没有高亮的，显示高亮按钮
            <TouchableOpacity style={styles.actionBarBtn} onPress={handleHighlight}>
              <Image
                source={require('@/assets/images/bible/icon-Highlight.png')}
                style={{ width: 28, height: 28, marginBottom: 2 }}
              />
              <Text style={styles.actionBarBtnText}>高亮</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity style={styles.actionBarBtn} onPress={handleCopy}>
            <Image
              source={require('@/assets/images/bible/icon-Copy2.png')}
              style={{ width: 28, height: 28, marginBottom: 2 }}
            />
            <Text style={styles.actionBarBtnText}>复制</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionBarBtn} onPress={handleNote}>
            <Image
              source={require('@/assets/images/bible/icon-Notes2.png')}
              style={{ width: 28, height: 28, marginBottom: 2 }}
            />
            <Text style={styles.actionBarBtnText}>笔记</Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity style={styles.cancelBarBtn} activeOpacity={0.9} onPress={onCancel}>
          <Text style={[styles.actionBarBtnText, { color: '#ffffff' }]}>取消</Text>
        </TouchableOpacity>
      </SafeAreaView>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  actionBar: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0
  },
  actionBarContent: {
    backgroundColor: '#333',
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 14,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    zIndex: 11,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8
  },
  actionBarBtn: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1
  },
  actionBarBtnText: {
    fontSize: getFontSize().smallMinusFontSize,
    lineHeight: getFontSize().smallMinusFontSize + 4,
    color: '#fff'
  },
  cancelBarBtn: {
    width: '100%',
    backgroundColor: '#333',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderTopWidth: 1,
    borderColor: '#dee2e6',
    borderBottomEndRadius: 16,
    borderBottomStartRadius: 16
  }
});

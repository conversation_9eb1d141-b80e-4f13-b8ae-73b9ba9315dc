import {
  Animated,
  Dimensions,
  Easing,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
  useWindowDimensions
} from 'react-native';
import { ChapterData, ETranslation, IChapter, IVerse, calculateVerseScrollPosition } from '@/utils/bibleUtils';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';
import { useFocusEffect, useNavigation, useRoute } from '@react-navigation/native';
import { useEventListener } from 'expo';

import BibleTools from './components/BibleTools';
import ChapterContainer from './components/ChapterContainer';
import ChapterSelector from './components/ChapterSelector';
import { getFontSize } from '@/utils/getFontSize';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';



interface RouteParams {
  bookId: number;
  bookName: string;
  chapter?: number;
  verse?: number;
}

export default function BibleChapterScreen() {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const { bookId = 1, bookName, chapter = 1, verse } = route.params as RouteParams;
  const flatListRef = useRef<FlatList<ChapterData>>(null);
  const scrollViewRef = useRef<ScrollView>(null);
  const stopLoadRef = useRef<boolean>(true);
  const itemHeightsRef = useRef<{ [key: number]: number }>({});

  const [allChaptersData, setAllChaptersData] = useState<ChapterData[]>([]);
  const [chaptersData, setChaptersData] = useState<ChapterData[]>([]);
  const [currentChapter, setCurrentChapter] = useState(chapter);
  const [currentTranslation, setCurrentTranslation] = useState<ETranslation[]>([]);
  const horizontalScrollX = useRef<number>(0);

  const totalChapters = allChaptersData.length;
  const chapterNumbers = Array.from({ length: totalChapters }, (_, i) => i + 1);
  const windowWidth = useWindowDimensions().width;
  const windowHeight = useWindowDimensions().height;
  const verseCircleSize = windowWidth / 7.5 - 5;

  // Function to check if a chapter is visible in the horizontal ScrollView
  // Wrapped in useCallback to prevent recreation on every render
  const isChapterVisible = useCallback(
    (chapter: number) => {
      // Calculate the position of the chapter in the ScrollView
      const chapterPositionRight = chapter * (verseCircleSize + 5);
      const chapterPositionLeft = (chapter - 1) * (verseCircleSize + 5);
      // Check if the chapter is within the visible area
      const visibleLeft = chapterPositionLeft >= horizontalScrollX.current;
      const visibleRight =
        chapterPositionRight >= horizontalScrollX.current &&
        chapterPositionRight < horizontalScrollX.current + windowWidth;
      return { visible: visibleRight && visibleLeft, visibleLeft };
    },
    [verseCircleSize, windowWidth]
  );
  // We'll define the effect after loadAllChapters is declared

  // Wrap loadChapterData in useCallback to avoid recreation on every render
  const loadChapterData = useCallback(async () => {
    try {
      // Get the selected translations from settings
      const settings = await getObjectAsync('bible.settings');
      let selectedTranslations: ETranslation[] = [ETranslation.cuvs];

      if (settings) {
        if (Array.isArray(settings)) {
          // New format: array of selected translations
          selectedTranslations = settings as ETranslation[];
        } else if (settings.versions && Array.isArray(settings.versions)) {
          // Old format: {versions: [...]} object
          selectedTranslations = settings.versions;
        }
      }

      // Set current translation
      setCurrentTranslation(selectedTranslations);

      // Create a map to store chapter data for each translation
      const translationsData: Record<ETranslation, IChapter | null> = {
        cuvs: null,
        cuvt: null,
        asv: null
      };

      // Load data for each selected translation
      for (const translation of selectedTranslations) {
        const storageKey = `bible.chapters.${bookId}.${translation}`;
        const chapterData = (await getObjectAsync(storageKey)) as IChapter;

        let hasData = false;

        if (chapterData && Object.keys(chapterData).length > 0) {
          hasData = true;
        }

        if (hasData) {
          translationsData[translation as ETranslation] = chapterData;
        }
      }

      // Use the first translation as the base structure
      const baseTranslation = selectedTranslations[0] as ETranslation;
      const baseChapterData = translationsData[baseTranslation];

      if (!baseChapterData) {
        console.error(`No data found for base translation ${baseTranslation}`);
        return null;
      }

      // Merge translations into a single chapter data structure
      const mergedChapterData: IChapter = {};

      Object.keys(baseChapterData).forEach((chapterNum) => {
        const verses = baseChapterData[chapterNum] || [];
        mergedChapterData[chapterNum] = verses.map((verse) => {
          const verseWithTranslations: IVerse = {
            ...verse,
            translations: {}
          };

          // Add the base translation content
          verseWithTranslations.translations![baseTranslation] = verse.content;

          // Add content from other translations
          for (const translation of selectedTranslations) {
            if (translation === baseTranslation) {
              continue;
            }

            const translationData = translationsData[translation as ETranslation];
            if (translationData && translationData[chapterNum]) {
              const matchingVerse = translationData[chapterNum]?.find((v) => v.verseId === verse.verseId);
              if (matchingVerse) {
                verseWithTranslations.translations![translation as ETranslation] = matchingVerse.content;
              }
            }
          }

          return verseWithTranslations;
        });
      });

      return mergedChapterData;
    } catch (error) {
      console.error('Failed to load chapter data:', error);
      return null;
    }
  }, [bookId]);

  // Use loadChapterData in loadAllChapters
  const loadAllChapters = useCallback(async () => {
    const chapterData = await loadChapterData();
    if (chapterData) {
      // Load all chapters data at once
      const allChapters: ChapterData[] = [];
      // Process verses: merge empty content verses with previous non-empty verse
      Object.keys(chapterData).forEach((chapterNum) => {
        const chapterNumber = parseInt(chapterNum);
        const verses = chapterData[chapterNum] || [];
        const processedVerses: IVerse[] = [];

        let i = 0;
        while (i < verses.length) {
          const currentVerse = verses[i] as IVerse;

          // If current verse has no content or empty content
          if (!currentVerse?.content || currentVerse.content.trim() === '') {
            // Look for next verse with content
            let j = i + 1;
            while (j < verses.length && (!verses[j]?.content || verses[j]?.content.trim() === '')) {
              j++;
            }

            // If we have a previous processed verse and found a next verse with content
            if (processedVerses.length > 0 && j < verses.length) {
              // Modify the previous verse to include this range
              const lastVerse = processedVerses[processedVerses.length - 1] as IVerse;
              lastVerse.verse = `${lastVerse?.verse}-${currentVerse?.verse}`;
            }

            i++;
            continue;
          }

          // Current verse has content, check if next verses are empty
          let j = i + 1;
          let endVerse = currentVerse.verse;

          while (j < verses.length && (!verses[j]?.content || verses[j]?.content.trim() === '')) {
            endVerse = verses[j]?.verse || 1;
            j++;
          }

          // Add the verse (possibly with merged range)
          processedVerses.push({
            ...currentVerse,
            verse: j > i + 1 ? `${currentVerse.verse}-${endVerse}` : currentVerse.verse
          });

          i = j;
        }

        allChapters.push({
          chapter: chapterNumber,
          verses: processedVerses
        });
      });

      // Sort chapters by number
      allChapters.sort((a, b) => a.chapter - b.chapter);

      // Set the total chapters data
      setAllChaptersData(allChapters);

      // Calculate the range of chapters to display
      const targetChapter = Math.min(chapter, allChapters.length);
      const startChapter = Math.max(1, targetChapter - 1);
      const endChapter = Math.min(allChapters.length, targetChapter + 1);

      // Set the initial chapters to display
      const initialChapters = allChapters.slice(startChapter - 1, endChapter);
      setChaptersData(initialChapters);

      // Set the current chapter
      setCurrentChapter(targetChapter);
      // Calculate the index of the target chapter in the initial data
      const targetIndex = initialChapters.findIndex((chapter) => chapter.chapter === targetChapter);

      if (targetIndex !== -1) {
        setTimeout(() => {
          console.log('itemHeightsRef.current[targetIndex]', itemHeightsRef.current);

          // Get the chapter data for precise calculation
          const targetChapterData = initialChapters[targetIndex];
          const chapterHeight = itemHeightsRef.current[targetChapter] || 0;

          // Calculate precise scroll position using the new method
          const preciseViewOffset =
            chapterHeight > 0 && targetChapterData && verse
              ? calculateVerseScrollPosition(
                  targetChapterData,
                  verse,
                  chapterHeight,
                  windowHeight,
                  verseCircleSize,
                  getFontSize().mediumFontSize,
                  windowWidth
                )
              : verseCircleSize;

          flatListRef.current?.scrollToIndex({
            index: targetIndex,
            animated: false,
            viewOffset: preciseViewOffset
          });

          // Initialize the horizontal scroll position
          const initialScrollX = (targetChapter - 1) * (verseCircleSize + 5);
          horizontalScrollX.current = initialScrollX;
          scrollViewRef.current?.scrollTo({
            x: initialScrollX,
            animated: false
          });
        }, 300);
      }

      setTimeout(() => {
        stopLoadRef.current = false;
      }, 300);
    }
  }, [chapter, verse, loadChapterData, verseCircleSize, windowWidth, windowHeight]);

  const handleChapterSelect = (targetChapter: number) => {
    stopLoadRef.current = true;
    setCurrentChapter(targetChapter);
    // Calculate the range of chapters to display
    const startChapter = Math.max(1, targetChapter - 1);
    const endChapter = Math.min(totalChapters, targetChapter + 1);

    // Update the chapters data with the new range
    const newChaptersData = allChaptersData.slice(startChapter - 1, endChapter);
    setChaptersData(newChaptersData);

    setTimeout(() => {
      flatListRef.current?.scrollToOffset({ offset: 0, animated: false });
      setTimeout(() => {
        flatListRef.current?.scrollToIndex({
          index: targetChapter === 1 ? 0 : 1,
          animated: false,
          viewOffset: verseCircleSize - 15
        });
        setTimeout(() => {
          stopLoadRef.current = false;
        }, 300);
      }, 100);
    }, 50);
  };
  useEffect(() => {
    const chapterVisible = isChapterVisible(currentChapter);
    if (!chapterVisible?.visible) {
      const targetScrollX = Math.min(
        chapterNumbers.length * (verseCircleSize + 5),
        currentChapter * (verseCircleSize + 5) -
          (!chapterVisible?.visibleLeft ? ((verseCircleSize + 5) * 3) / 2 : windowWidth - (verseCircleSize + 5) / 2)
      );
      scrollViewRef.current?.scrollTo({
        x: targetScrollX,
        animated: true
      });
    }
    setObjectAsync('bible.currentVerse', { verseId: allChaptersData[currentChapter - 1]?.verses?.[0]?.verseId });
  }, [currentChapter, allChaptersData, chapterNumbers.length, isChapterVisible, verseCircleSize, windowWidth]);

  // Define loadAllChaptersEffect after loadAllChapters is declared
  const loadAllChaptersEffect = useCallback(() => {
    loadAllChapters();
  }, [loadAllChapters]);

  useFocusEffect(loadAllChaptersEffect);

  useSetNavigationOptions({
    title: bookName,
    headerRight: () => (
      <TouchableOpacity style={styles.settingsButton} onPress={() => navigation.navigate('BibleSetting' as never)}>
        <Image style={styles.bibleIcon} source={require('@/assets/images/icon-Bible2.jpg')}></Image>
      </TouchableOpacity>
    )
  });

  // 高光和操作栏相关state
  const [highlightedVerseIds, setHighlightedVerseIds] = useState<number[]>([]);
  const [selectedVerseIds, setSelectedVerseIds] = useState<number[]>([]); // 新增：选中状态
  const [selectedVerse, setSelectedVerse] = useState<{ chapter: number; verse: IVerse } | null>(null);
  const [actionBarVisible, setActionBarVisible] = useState(false);
  const actionBarAnim = useRef(new Animated.Value(0)).current;
  const [notesData, setNotesData] = useState<Record<number, Record<number, any>>>({});

  // 加载笔记数据
  const loadNotesData = useCallback(async () => {
    try {
      const allNotes: Record<number, Record<number, any>> = {};

      // 为当前显示的章节加载笔记数据
      for (const chapterData of chaptersData) {
        const storageKey = `bible.notes.${bookId}.${chapterData.chapter}`;
        const chapterNotes = await getObjectAsync(storageKey) || {};

        // 合并到总的笔记数据中
        Object.assign(allNotes, chapterNotes);
      }

      setNotesData(allNotes);
    } catch (error) {
      console.error('加载笔记数据失败:', error);
    }
  }, [bookId, chaptersData]);

  // 当章节数据变化时重新加载笔记
  useEffect(() => {
    if (chaptersData.length > 0) {
      loadNotesData();
    }
  }, [loadNotesData]);

  // 监听笔记更新事件
  useEventListener(globalThis.eventEmitter, 'BibleNotesUpdated', () => {
    loadNotesData();
  });

  // 处理笔记点击
  const handleNotePress = (verseId: number, chapterNumber: number, verseNumber: number | string) => {
    navigation.navigate('BibleNote' as never, {
      verseId,
      bookName,
      chapterNumber,
      verseNumber
    } as never);
  };

  // 切换选中状态（checkbox）
  const toggleSelection = (verseId: number) => {
    setSelectedVerseIds((prev) => {
      if (prev.includes(verseId)) {
        return prev.filter((id) => id !== verseId);
      } else {
        return [...prev, verseId];
      }
    });
  };

  // 高光切换并存储
  const toggleHighlight = async (verseId: number) => {
    setHighlightedVerseIds((prev) => {
      let newIds: number[];
      const isHighlighting = !prev.includes(verseId);

      if (isHighlighting) {
        newIds = [...prev, verseId];
      } else {
        newIds = prev.filter((id) => id !== verseId);
      }

      // 为所有当前翻译版本存储高亮
      if (currentTranslation && currentTranslation.length > 0) {
        currentTranslation.forEach(async (translation) => {
          const key = `bible.highlightedVerses.${translation}`;
          console.log('key', key);

          // 获取该翻译版本的现有高亮数据
          const existingData = await getObjectAsync(key);
          const chapterKey = `${bookId}:${currentChapter}`;
          const newData = { ...(existingData || {}) };

          // 获取该翻译版本当前章节的高亮列表
          const existingHighlights = newData[chapterKey] || [];

          if (isHighlighting) {
            // 添加高亮 - 只在不存在时添加
            if (!existingHighlights.includes(verseId)) {
              newData[chapterKey] = [...existingHighlights, verseId];
            }
          } else {
            // 移除高亮 - 只移除当前verseId
            newData[chapterKey] = existingHighlights.filter((id: number) => id !== verseId);
          }

          // 保存该翻译版本的高亮数据
          await setObjectAsync(key, newData);
        });
      }

      return newIds;
    });
  };

  // 进入页面时读取高亮
  useEffect(() => {
    // 读取高亮 - 加载当前章节前后两章的高亮数据
    (async () => {
      if (currentTranslation && currentTranslation.length > 0) {
        // 使用第一个翻译版本作为主要版本来读取高亮
        const mainVersion = currentTranslation[0];
        const key = `bible.highlightedVerses.${mainVersion}`;
        const data = await getObjectAsync(key);

        if (data) {
          let allHighlightedIds: number[] = [];
          // 加载前一章、当前章、后一章的高亮数据
          for (let chapter = currentChapter - 1; chapter <= currentChapter + 1; chapter++) {
            if (chapter > 0) {
              // 确保章节号有效
              const chapterKey = `${bookId}:${chapter}`;
              const chapterHighlights = data[chapterKey] || [];
              allHighlightedIds = [...allHighlightedIds, ...chapterHighlights];
            }
          }
          setHighlightedVerseIds(allHighlightedIds);
        } else {
          setHighlightedVerseIds([]);
        }
      }
    })();
  }, [bookId, currentChapter, currentTranslation]);

  // 显示/隐藏操作栏动画
  useEffect(() => {
    if (selectedVerse) {
      setActionBarVisible(true);
      Animated.timing(actionBarAnim, {
        toValue: 1,
        duration: 250,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true
      }).start();
    } else {
      Animated.timing(actionBarAnim, {
        toValue: 0,
        duration: 200,
        easing: Easing.in(Easing.ease),
        useNativeDriver: true
      }).start(() => setActionBarVisible(false));
    }
  }, [selectedVerse, actionBarAnim]);

  return (
    <View style={styles.container}>
      <ChapterSelector
        ref={scrollViewRef}
        chapterNumbers={chapterNumbers}
        currentChapter={currentChapter}
        verseCircleSize={verseCircleSize}
        onChapterSelect={handleChapterSelect}
        onScroll={(event) => {
          horizontalScrollX.current = event.nativeEvent.contentOffset.x;
        }}
      />

      <FlatList
        ref={flatListRef}
        data={chaptersData}
        initialNumToRender={chaptersData.length}
        keyExtractor={(item) => item.chapter.toString()}
        onScroll={({ nativeEvent: { contentOffset } }) => {
          if (stopLoadRef.current) {
            return;
          }
          // Calculate current chapter based on scroll position
          const y = contentOffset.y;
          let totalHeight = 0;
          let currentVisibleChapter = currentChapter;

          // Find which chapter is most visible in the viewport
          for (let i = 0; i < chaptersData.length; i++) {
            const chapter = chaptersData[i];
            if (chapter) {
              const chapterHeight = itemHeightsRef.current[chapter.chapter] || 0;

              // If scroll position is within this chapter's range
              if (y >= totalHeight && y < totalHeight + chapterHeight) {
                currentVisibleChapter = chapter.chapter;
                break;
              }

              totalHeight += chapterHeight;
            }
          }

          // Update current chapter if it changed
          if (currentVisibleChapter !== currentChapter) {
            setCurrentChapter(currentVisibleChapter);
          }
        }}
        onEndReached={() => {
          if (stopLoadRef.current) {
            return;
          }
          stopLoadRef.current = true;
          if (currentChapter < totalChapters && chaptersData) {
            const currentChapter = chaptersData[chaptersData.length - 1]?.chapter || 1;
            const nextChapter = currentChapter + 1;

            // Calculate the range of chapters to display
            const startChapter = Math.max(1, nextChapter - 1);
            const endChapter = Math.min(totalChapters, nextChapter + 1);

            // Update the chapters data with the new range
            setChaptersData(allChaptersData.slice(startChapter - 1, endChapter));
            setTimeout(() => {
              flatListRef.current?.scrollToIndex({
                index: 0,
                animated: false,
                viewOffset: -(itemHeightsRef.current[currentChapter] || 0) + Dimensions.get('window').height - 200
              });
              setTimeout(() => {
                stopLoadRef.current = false;
              }, 300);
            }, 100);
          }
        }}
        onStartReached={({ distanceFromStart }) => {
          if (stopLoadRef.current || distanceFromStart > 20) {
            return;
          }
          if (currentChapter > 1 && chaptersData) {
            stopLoadRef.current = true;
            const currentChapter = chaptersData[0]?.chapter || 1;
            const prevChapter = currentChapter - 1;

            // Calculate the range of chapters to display
            const startChapter = Math.max(1, prevChapter - 1);
            const endChapter = Math.min(totalChapters, prevChapter + 1);

            // Update the chapters data with the new range
            setChaptersData(allChaptersData.slice(startChapter - 1, endChapter));

            setTimeout(() => {
              flatListRef.current?.scrollToIndex({
                index: 1,
                animated: false,
                viewOffset: 200
              });
              setTimeout(() => {
                stopLoadRef.current = false;
              }, 300);
            }, 100);
          }
        }}
        renderItem={({ item }) => (
          <ChapterContainer
            key={item.chapter}
            chapter={item}
            highlightedVerseIds={highlightedVerseIds}
            selectedVerseIds={selectedVerseIds}
            actionBarVisible={actionBarVisible}
            notesData={notesData}
            onVersePress={(verseId) => {
              // 点击时不做任何操作，只有hover效果
              if (actionBarVisible) {
                toggleSelection(verseId);
              }
            }}
            onVerseLongPress={(verse, chapter) => setSelectedVerse({ chapter, verse })}
            onNotePress={handleNotePress}
            onLayout={(event) => {
              // Store the height of each chapter item
              const { height } = event.nativeEvent.layout;
              itemHeightsRef.current[item.chapter] = height;
            }}
          />
        )}
        contentContainerStyle={{
          paddingHorizontal: 15,
          paddingBottom: 30,
          paddingTop: verseCircleSize
        }}
        onScrollToIndexFailed={(info) => {
          // First scroll to the beginning of the list
          flatListRef.current?.scrollToOffset({
            offset: 0,
            animated: false
          });
          // Then try to scroll to the target index
          setTimeout(() => {
            if (flatListRef.current) {
              flatListRef.current.scrollToIndex({
                index: info.index,
                animated: false,
                viewPosition: 0
              });
            }
          }, 200);
        }}
      />
      <BibleTools
        bookName={bookName}
        visible={actionBarVisible}
        anim={actionBarAnim}
        selectedVerseIds={selectedVerseIds}
        highlightedVerseIds={highlightedVerseIds}
        chaptersData={chaptersData}
        currentTranslation={currentTranslation}
        onHighlight={(verseIds) => {
          verseIds.forEach((verseId) => toggleHighlight(verseId));
          setSelectedVerse(null);
          setSelectedVerseIds([]);
        }}
        onUnhighlight={(verseIds) => {
          verseIds.forEach((verseId) => toggleHighlight(verseId));
          setSelectedVerse(null);
          setSelectedVerseIds([]);
        }}
        onCopy={() => {
          setSelectedVerse(null);
          setSelectedVerseIds([]);
        }}
        onNote={(verseIds, bookName, verseRange) => {
          // 导航到笔记创建页面，只传递verseIds、bookName和verseRange
          navigation.navigate('BibleNoteCreate', {
            verseIds,
            bookName,
            verseRange
          });

          setSelectedVerse(null);
          setSelectedVerseIds([]);
        }}
        onCancel={() => {
          setSelectedVerse(null);
          setSelectedVerseIds([]);
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#fff' },
  settingsButton: { padding: 5 },
  bibleIcon: {
    width: 32,
    height: 32
  }
});

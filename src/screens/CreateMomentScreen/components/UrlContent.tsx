import { CreateMomentContext, UrlFormDataProps } from '@/screens/CreateMomentScreen/utils';
import React, { useContext, useEffect, useState } from 'react';
import { TextInput, View } from 'react-native';

import FormItem from '@/screens/CreateMomentScreen/components/FormItem';
import UrlSelectImages from '@/screens/CreateMomentScreen/components/UrlSelectImages';
import { getFontSize } from '@/utils/getFontSize';
import { i18n2 } from '@/utils/i18n2';
import { showMessage } from '@/components/MessageBox';

interface UrlContentProps {
  data: Partial<UrlFormDataProps>;
  onChange: (v: Partial<UrlFormDataProps>) => void;
}

const UrlContent: React.FC<UrlContentProps> = ({ data, onChange }) => {
  const { setBusy } = useContext(CreateMomentContext);
  const { smallFontSize } = getFontSize();
  const [urlParsed, setUrlParsed] = useState<boolean>(!!data?.image);
  const dataChange = (v: string, key: keyof UrlFormDataProps) => {
    onChange({ ...data, [key]: v });
  };
  const [images, setImages] = useState<string[]>(data?.image ? [data.image] : []);

  // 处理预填充数据
  useEffect(() => {
    if (data?.image && !urlParsed) {
      setUrlParsed(true);
      setImages([data.image]);
    }
    // 如果有预填充的URL但没有image，也需要设置urlParsed为true
    if (data?.url && !data?.image && !urlParsed) {
      setUrlParsed(true);
    }
  }, [data, urlParsed]);

  const reset = (hideMessage?: boolean) => {
    setImages([]);
    setUrlParsed(false);
    onChange({ ...data, image: '', title: '' });
    if (!hideMessage) {
      showMessage({
        message: i18n2.t('CreateMomentScreen.InvalidUrl'),
        duration: 2000
      });
    }
  };
  const parseUrl = async () => {
    let { url } = data;
    try {
      url = (url || '').trim();
      if (url.length === 0) {
        reset(true);
        return;
      }
      setBusy?.(true);
      const result = await globalThis.dsObject.getMomentPreview(url);
      if (!result || !result?.body?.images?.length) {
        reset();
        return;
      }

      const { title, images } = result.body;
      if (images?.length) {
        setImages([...new Set([...images])]);
      }
      setUrlParsed(true);
      onChange({ ...data, image: images[0], title: title || i18n2.t('Common.Title') });
    } catch (err) {
      alert(err);
      reset();
    } finally {
      setBusy?.(false);
    }
  };
  return (
    <View>
      <FormItem label={i18n2.t('Common.EnterURL')}>
        <TextInput
          multiline
          numberOfLines={4}
          style={{
            backgroundColor: '#ffffff',
            padding: 8,
            lineHeight: smallFontSize + 4,
            fontSize: smallFontSize,
            textAlignVertical: 'top'
          }}
          value={data?.url}
          onChangeText={(v) => {
            if (!v) {
              reset(true);
            } else {
              dataChange(v, 'url');
            }
          }}
          placeholder={i18n2.t('Common.InputPrompt')}
          onBlur={() => {
            parseUrl();
          }}
          onSubmitEditing={() => {
            parseUrl();
          }}
        />
      </FormItem>
      {urlParsed && (
        <>
          <FormItem label={i18n2.t('Common.ConfirmTitle')}>
            <TextInput
              style={{
                backgroundColor: '#ffffff',
                padding: 8,
                lineHeight: smallFontSize + 4,
                fontSize: smallFontSize,
                textAlignVertical: 'center'
              }}
              value={data?.title}
              onChangeText={(v) => {
                dataChange(v, 'title');
              }}
              placeholder={i18n2.t('Common.InputPrompt')}
            />
          </FormItem>
          <FormItem label={i18n2.t('Common.SelectImage')}>
            <UrlSelectImages
              images={images}
              value={data?.image}
              onChange={(v) => {
                dataChange(v, 'image');
              }}
            />
          </FormItem>
          <FormItem label={i18n2.t('Common.CommentOptional')}>
            <TextInput
              multiline
              numberOfLines={4}
              style={{
                backgroundColor: '#ffffff',
                padding: 8,
                lineHeight: smallFontSize + 4,
                fontSize: smallFontSize,
                height: 16 + 4 * (smallFontSize + 4),
                textAlignVertical: 'top'
              }}
              value={data?.description}
              onChangeText={(v) => {
                dataChange(v, 'description');
              }}
              placeholder={i18n2.t('Common.InputPrompt')}
            />
          </FormItem>
        </>
      )}
    </View>
  );
};

export default UrlContent;

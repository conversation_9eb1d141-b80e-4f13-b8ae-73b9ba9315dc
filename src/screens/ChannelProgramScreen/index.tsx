import { Image, SafeAreaView, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { getCurrentUser, getHttpsServer } from '@/utils/user';
import { useCallback, useEffect, useState } from 'react';
import { useNavigation, useRoute } from '@react-navigation/native';

import ActionBar from '@/screens/ChannelProgramScreen/ActionBar';
import { Colors } from '@/styles/colors';
import { IProgram } from '@/utils/channel/IChannel';
/* eslint-disable @typescript-eslint/no-explicit-any */
import Preview from '@/components/ProgramPreview';
import React from 'react';
import { StyleSheet } from 'react-native';
import { getFontSize } from '@/utils/getFontSize';
import { getUrlByParams } from '@/utils/url';
import { useSetNavigationOptions } from '@/hooks/useSetNavigationOptions';

const ChannelProgramScreen = () => {
  const { params } = useRoute();
  const navigation = useNavigation<any>();
  const { channelId, programId } = params as any;
  const [detail, setDetail] = useState<IProgram>();

  const initData = useCallback(async () => {
    global.dsObject.putProgramViews(programId);
    const [resp, channelResp] = await Promise.all([
      global.dsObject.getProgram(programId),
      global.dsObject.getChannel(channelId)
    ]);
    if (resp?.body) {
      setDetail({ ...resp?.body, channelName: channelResp?.body?.name });
    }
  }, [programId, channelId]);
  useEffect(() => {
    initData();
  }, [initData]);
  const getPreviewUrl = (v: string, isNotId?: boolean) => {
    return getUrlByParams(getHttpsServer(`/channels/${channelId}/content/${v}`), {
      token: getCurrentUser().getAccessToken(),
      programId: isNotId ? undefined : programId,
      time: new Date().getTime().toString()
    });
  };
  console.log("getPreviewUrl('cover.jpg', true)", getPreviewUrl('cover.jpg', true));
  useSetNavigationOptions({
    title: '',
    headerRight: () => (
      <TouchableOpacity
        onPress={() => {
          navigation.navigate('ChannelDetail', {
            channelId,
            channelName: detail?.channelName
          });
        }}
        style={{ flexDirection: 'row', alignItems: 'center', gap: 5 }}>
        <Text style={{ fontSize: 14, color: Colors.text }}>{detail?.channelName || ''}</Text>
        <Image style={{ width: 30, height: 30, borderRadius: 15 }} source={{ uri: getPreviewUrl('cover.jpg', true) }} />
      </TouchableOpacity>
    )
  });
  return !detail?.title ? null : (
    <>
      <ScrollView>
        <View style={styles.channelPreviewArticle}>
          <Text style={styles.channelPreviewArticleTitle}>{detail.title}</Text>
          <Text style={styles.channelPreviewArticleSubtitle}>{detail.subtitle}</Text>
          <View style={styles.channelPreviewArticleTags}>
            {detail?.tags
              ?.split(',')
              ?.filter((item) => !!item.trim())
              ?.map((item, index) => (
                <View style={styles.channelPreviewArticleTagsItem} key={item}>
                  <Text style={styles.channelPreviewArticleTagsText} key={`tag_${index}`}>
                    #{item.trim()}
                  </Text>
                </View>
              ))}
          </View>
          {detail.content && <Preview data={detail.content} getPreviewUrl={getPreviewUrl} />}
        </View>
      </ScrollView>
      <SafeAreaView>
        <ActionBar program={detail} navigation={navigation} />
      </SafeAreaView>
    </>
  );
};

export default ChannelProgramScreen;

const styles = StyleSheet.create({
  channelPreviewArticle: {
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 40
  },
  channelPreviewArticleTitle: {
    fontSize: getFontSize().X2LargeFontSize,
    lineHeight: getFontSize().X2LargeFontSize + 10,
    fontWeight: 'bold',
    color: '#000000'
  },
  channelPreviewArticleSubtitle: {
    fontSize: getFontSize().smallFontSize,
    lineHeight: getFontSize().smallFontSize + 10,
    color: '#999999'
  },
  channelPreviewArticleTags: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 10,
    marginBottom: 16
  },
  channelPreviewArticleTagsItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#acacac'
  },
  channelPreviewArticleTagsText: {
    color: '#666666',
    fontSize: getFontSize().smallFontSize,
    lineHeight: getFontSize().smallFontSize + 10
  }
});

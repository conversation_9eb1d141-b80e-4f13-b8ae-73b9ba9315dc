import * as Clipboard from 'expo-clipboard';

import { Image, Linking, Modal, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { NavigationProp, ParamListBase } from '@react-navigation/native';
import React, { useState } from 'react';

import { IProgram } from '@/utils/channel/IChannel';
import ResponsiveImage from '@/components/ResponsiveImage';
import { Share } from 'react-native';
import { getFontSize } from '@/utils/getFontSize';
import { getHttpsServer } from '@/utils/user';
import { getUrlByParams } from '@/utils/url';
import { goToChat } from '@/utils/goToScreen';
import { i18n2 } from '@/utils/i18n2';
import { useGroups } from '@/hooks/useGroups';

interface IGroupItemProps {
  groupId: string;
  name: string;
  status?: number;
  isOneOnOneGroup?: boolean;
}

interface ActionBarProps {
  program: IProgram;
  onLike?: (liked: boolean) => void;
  onCollect?: (collected: boolean) => void;
  navigation: NavigationProp<ParamListBase>;
}

const ActionBar: React.FC<ActionBarProps> = ({ program, onLike, onCollect, navigation }) => {
  const [liked, setLiked] = useState(program.isLiked ?? false);
  const [collected, setCollected] = useState(program.isCollected ?? false);
  const [likeCount, setLikeCount] = useState(program.likeCount || 0);
  const [collectCount, setCollectCount] = useState(program.collect || 0);
  const [shareCount, setShareCount] = useState(program.share || 0);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [sending, setSending] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<IGroupItemProps | null>(null);

  const { groups: studyGroups } = useGroups(true);
  const fontSize = getFontSize();

  const toggleLike = async () => {
    const newLiked = !liked;
    const res = newLiked
      ? await global.dsObject.postProgramLike(program.programId)
      : await global.dsObject.postProgramUnlike(program.programId);
    if (res) {
      setLiked(newLiked);
      setLikeCount((prev) => (newLiked ? prev + 1 : prev - 1));
      onLike?.(newLiked);
    }
  };

  const toggleCollect = async () => {
    const newCollected = !collected;
    const res = newCollected
      ? await global.dsObject.postProgramCollect(program.programId)
      : await global.dsObject.postProgramUncollect(program.programId);
    if (res) {
      setCollected(newCollected);
      setCollectCount((prev) => (newCollected ? prev + 1 : prev - 1));
      onCollect?.(newCollected);
    }
  };

  const shareUrl = `https://idigest.app/preview/#/programDetail?channelId=${program.channelId}&programId=${program.programId}`;
  const shareContent = `${program.title}\n${program.subtitle || ''}\n${shareUrl}`;

  // 统一分享统计方法
  const statShare = async () => {
    try {
      const res = await global.dsObject.putProgramShare(program.programId);
      if (res) {
        setShareCount((prev) => prev + 1);
      }
    } catch (e) {
      // ignore
    }
  };

  const handleMoments = async () => {
    setShowShareModal(false);
    navigation.navigate('CreateMoment', {
      defaultAccept: true,
      defaultFormType: 0,
      defaultFormData: {
        url: shareUrl,
        title: program.title || '',
        description: program.subtitle || '',
        image: getPreviewUrl(program.cover || '')
      }
    });
    await statShare();
  };
  const handleShareOut = async () => {
    try {
      await Share.share({
        message: shareContent
      });
      await statShare();
    } catch (e) {
      // ignore
    }
  };

  const handleFellowship = () => {
    setShowShareModal(false);
    setTimeout(() => {
      navigation.navigate('Select', {
        choices: studyGroups,
        getDisplayName: (item: IGroupItemProps) => item.name,
        onSelect: (group: IGroupItemProps) => {
          navigation.goBack();
          setSelectedGroup(group);
          setShowConfirmModal(true);
        },
        title: i18n2.t('ChannelProgramScreen.SelectGroup'),
        skipNavigateBack: true
      });
    }, 300);
  };

  const handleSendMessage = async () => {
    if (!selectedGroup) {
      return;
    }
    setSending(true);
    await createDiscussionAndNavigate(selectedGroup);
    setSending(false);
    setShowConfirmModal(false);
    await statShare();
  };

  const createDiscussionAndNavigate = async (selectedGroup: IGroupItemProps) => {
    const message = `${shareContent}&cover=${program.cover}`;

    // 直接发送消息到 groupId
    await globalThis.dsObject.sendMessage({
      room: selectedGroup.groupId,
      message,
      isAnonymous: false
    });

    // 跳转到聊天页面
    goToChat(selectedGroup, navigation);
  };

  const handleCopyLink = async () => {
    await Clipboard.setStringAsync(shareUrl);
    setShowShareModal(false);
    await statShare();
  };
  const handleOpenBrowser = async () => {
    Linking.openURL(`${shareUrl}&_t=${Date.now()}`);
    setShowShareModal(false);
    await statShare();
  };
  const getPreviewUrl = (v: string) => {
    return getUrlByParams(getHttpsServer(`/channels/${program.channelId}/content/${v}`), {
      programId: program.programId.toString(),
      time: new Date().getTime().toString()
    });
  };

  return (
    <>
      <View style={styles.actionBar}>
        <TouchableOpacity style={styles.actionItem} onPress={toggleLike}>
          <Image
            source={
              liked
                ? require('@/assets/images/channel/icon-Like2.png')
                : require('@/assets/images/channel/icon-Like.png')
            }
            style={styles.icon}
          />
          <Text style={[styles.actionText, { fontSize: fontSize.smallFontSize }]}>{likeCount}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionItem} onPress={toggleCollect}>
          <Image
            source={
              collected
                ? require('@/assets/images/channel/icon-MyCollections2.png')
                : require('@/assets/images/channel/icon-MyCollections.png')
            }
            style={styles.icon}
          />
          <Text style={[styles.actionText, { fontSize: fontSize.smallFontSize }]}>{collectCount}</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionItem} onPress={() => setShowShareModal(true)}>
          <Image source={require('@/assets/images/channel/icon-ExternalShare.png')} style={styles.icon} />
          <Text style={[styles.actionText, { fontSize: fontSize.smallFontSize }]}>{shareCount}</Text>
        </TouchableOpacity>
        <View style={styles.actionItem}>
          <Image source={require('@/assets/images/channel/icon_view.png')} style={styles.icon} />
          <Text style={[styles.actionText, { fontSize: fontSize.smallFontSize }]}>{(program.views || 0) + 1}</Text>
        </View>
      </View>
      <Modal visible={showShareModal} animationType='slide' transparent onRequestClose={() => setShowShareModal(false)}>
        <TouchableOpacity style={styles.modalMask} activeOpacity={1} onPress={() => setShowShareModal(false)}>
          <TouchableOpacity
            style={[styles.modalContent, { paddingHorizontal: 16 }]}
            activeOpacity={1}
            onPress={(e) => e.stopPropagation()}>
            {/* 卡片预览 */}
            <View style={styles.previewRow}>
              <ResponsiveImage style={styles.coverImg} source={{ uri: getPreviewUrl(program.cover || '') }} />
              <View style={styles.previewText}>
                <Text style={[styles.title, { fontSize: fontSize.mediumFontSize }]}>{program.title}</Text>
                {program.subtitle ? (
                  <Text style={[styles.subtitle, { fontSize: fontSize.smallMinusFontSize }]}>{program.subtitle}</Text>
                ) : null}
              </View>
            </View>
            {/* 分享操作 */}
            <View style={styles.shareRow}>
              <TouchableOpacity style={styles.shareBtn} onPress={handleFellowship}>
                <Image source={require('@/assets/images/channel/icon-Fellowship.png')} style={styles.shareIcon} />
                <Text style={[styles.shareLabel, { fontSize: fontSize.XSmallMinusFontSize }]}>
                  {i18n2.t('ChannelProgramScreen.ShareFellowship')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.shareBtn} onPress={handleMoments}>
                <Image source={require('@/assets/images/channel/icon-Moments.png')} style={styles.shareIcon} />
                <Text style={[styles.shareLabel, { fontSize: fontSize.XSmallMinusFontSize }]}>
                  {i18n2.t('ChannelProgramScreen.ShareMoments')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.shareBtn} onPress={handleShareOut}>
                <Image source={require('@/assets/images/icon-Export.png')} style={styles.shareIcon} />
                <Text style={[styles.shareLabel, { fontSize: fontSize.XSmallMinusFontSize }]}>
                  {i18n2.t('ChannelProgramScreen.ShareOut')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.shareBtn} onPress={handleCopyLink}>
                <Image source={require('@/assets/images/channel/icon-Weblink.png')} style={styles.shareIcon} />
                <Text style={[styles.shareLabel, { fontSize: fontSize.XSmallMinusFontSize }]}>
                  {i18n2.t('ChannelProgramScreen.CopyLink')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.shareBtn} onPress={handleOpenBrowser}>
                <Image source={require('@/assets/images/channel/icon-WebBrowser.png')} style={styles.shareIcon} />
                <Text style={[styles.shareLabel, { fontSize: fontSize.XSmallMinusFontSize }]}>
                  {i18n2.t('ChannelProgramScreen.OpenInBrowser')}
                </Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
      </Modal>
      {/* 独立的二次确认弹窗 */}
      <Modal
        visible={showConfirmModal}
        transparent
        animationType='fade'
        onRequestClose={() => setShowConfirmModal(false)}>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: 'rgba(0,0,0,0.3)' }}>
          <View style={{ backgroundColor: '#fff', borderRadius: 10, padding: 24, minWidth: 300, maxWidth: 350 }}>
            {/* 节目卡片预览 */}
            <View style={styles.confirmPreviewRow}>
              <ResponsiveImage style={styles.confirmCoverImg} source={{ uri: getPreviewUrl(program.cover || '') }} />
              <View style={styles.confirmPreviewText}>
                <Text style={[styles.confirmTitle, { fontSize: fontSize.mediumFontSize }]} numberOfLines={2}>
                  {program.title}
                </Text>
                {program.subtitle ? (
                  <Text style={[styles.confirmSubtitle, { fontSize: fontSize.smallMinusFontSize }]} numberOfLines={1}>
                    {program.subtitle}
                  </Text>
                ) : null}
              </View>
            </View>

            <Text style={{ fontSize: fontSize.mediumFontSize, marginBottom: 20, textAlign: 'center' }}>
              {i18n2?.tValue?.('ChannelProgramScreen.ConfirmSend', selectedGroup?.name || '')}
            </Text>

            {/* 按钮左右分布 */}
            <View style={styles.confirmButtonRow}>
              <TouchableOpacity
                style={styles.confirmCancelButton}
                onPress={() => setShowConfirmModal(false)}
                disabled={sending}>
                <Text style={[styles.confirmCancelText, { fontSize: fontSize.mediumFontSize }]}>
                  {i18n2.t('Common.Cancel')}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.confirmSendButton} onPress={handleSendMessage} disabled={sending}>
                <Text style={[styles.confirmSendText, { fontSize: fontSize.mediumFontSize }]}>
                  {sending ? i18n2.t('ChannelProgramScreen.Sending') : i18n2.t('ChannelProgramScreen.SendMessage')}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 12,
    borderTopWidth: 1,
    borderColor: '#eee',
    backgroundColor: '#fff'
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  actionText: {
    marginLeft: 4,
    color: '#333'
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain'
  },
  modalMask: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.35)',
    justifyContent: 'flex-end'
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 20
  },
  previewRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 18,
    padding: 16
  },
  coverImg: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#eee',
    marginRight: 14
  },
  previewText: {
    flex: 1
  },
  title: {
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 2
  },
  subtitle: {
    color: '#666'
  },
  shareRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10
  },
  shareBtn: {
    alignItems: 'center',
    flex: 1
  },
  shareIcon: {
    width: 36,
    height: 36,
    marginBottom: 4,
    resizeMode: 'contain'
  },
  shareLabel: {
    color: '#333'
  },
  confirmPreviewRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 18,
    padding: 16
  },
  confirmCoverImg: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#eee',
    marginRight: 14
  },
  confirmPreviewText: {
    flex: 1
  },
  confirmTitle: {
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 2
  },
  confirmSubtitle: {
    color: '#666'
  },
  confirmButtonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12
  },
  confirmCancelButton: {
    flex: 1,
    borderRadius: 6,
    padding: 12,
    alignItems: 'center',
    backgroundColor: '#f5f5f5'
  },
  confirmCancelText: {
    color: '#888'
  },
  confirmSendButton: {
    flex: 1,
    borderRadius: 6,
    padding: 12,
    alignItems: 'center',
    backgroundColor: '#3478f6'
  },
  confirmSendText: {
    color: '#fff'
  }
});

export default ActionBar;

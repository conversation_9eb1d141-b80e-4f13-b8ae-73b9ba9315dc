import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { OkButton } from '@/components/navigation/NavigationButtons';
import { i18n2 } from '@/utils/i18n2';
import { getFontSize } from '@/utils/getFontSize';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';
import { parseVerseId } from '@/utils/bibleUtils/utils';
import {useSetNavigationOptions} from '@/hooks/useSetNavigationOptions';

interface RouteParams {
  verseIds: number[];
  bookName: string;
  verseRange: string;
}

export default function BibleNoteCreateScreen() {
  const navigation = useNavigation();
  const route = useRoute();
  const { verseIds, bookName, verseRange } = route.params as RouteParams;
  const [noteContent, setNoteContent] = useState('');
  useSetNavigationOptions({
    title: '添加笔记'
  });
  const handleSave = async () => {
    if (!noteContent.trim()) {
      return;
    }

    try {
      // 为每个选中的经节保存笔记
      for (const verseId of verseIds) {
        const { bookId, chapter } = parseVerseId(verseId);
        const storageKey = `bible.notes.${bookId}.${chapter}`;

        // 获取现有的笔记数据
        const existingNotes = await getObjectAsync(storageKey) || {};

        // 创建笔记标题：书卷名+章节+经节信息
        const noteTitle = `${bookName} ${verseRange}`;

        // 创建新的笔记对象
        const newNote = {
          title: noteTitle,
          content: noteContent.trim(),
          createdAt: new Date().toISOString()
        };

        // 如果该verseId还没有笔记，创建数组；否则添加到现有数组
        if (!existingNotes[verseId]) {
          existingNotes[verseId] = [];
        }
        existingNotes[verseId].push(newNote);

        // 存储到本地
        await setObjectAsync(storageKey, existingNotes);
      }

      console.log('笔记保存成功');
      // 发送事件通知刷新笔记数据
      if (globalThis.eventEmitter && globalThis.eventEmitter.emit) {
        globalThis.eventEmitter.emit('BibleNotesUpdated');
      }
      navigation.goBack();
    } catch (error) {
      console.error('保存笔记失败:', error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.verseText}>经文：{bookName} {verseRange}</Text>
        <TextInput
          style={styles.noteInput}
          multiline
          numberOfLines={10}
          placeholder={i18n2.t('BibleNoteCreateScreen.NotePlaceholder')}
          value={noteContent}
          onChangeText={setNoteContent}
          textAlignVertical='top'
        />
      </ScrollView>

      {/* 保存按钮 */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
          <Text style={styles.saveButtonText}>{i18n2.t('BibleNoteCreateScreen.Save')}</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

BibleNoteCreateScreen.navigationOptions = () => ({
  title: i18n2.t('BibleNoteCreateScreen.Title'),
  headerLeft: () => (
    <OkButton
      onPress={() => {
        // 返回上一页
        if (globalThis.eventEmitter && globalThis.eventEmitter.emit) {
          globalThis.eventEmitter.emit('BibleNoteCreateGoBack');
        }
      }}
    />
  )
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff'
  },
  content: {
    flex: 1,
    padding: 20
  },
  verseText: {
    fontSize: getFontSize().mediumFontSize,
    lineHeight: getFontSize().mediumFontSize * 1.5,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  noteInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 12,
    fontSize: getFontSize().smallFontSize,
    lineHeight: getFontSize().smallFontSize * 1.5,
    minHeight: 200,
    textAlignVertical: 'top'
  },
  footer: {
    padding: 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0'
  },
  saveButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center'
  },
  saveButtonText: {
    color: '#fff',
    fontSize: getFontSize().mediumFontSize,
    fontWeight: '600'
  }
});

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  TextInput,
  Image
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { OkButton } from '@/components/navigation/NavigationButtons';
import { getFontSize } from '@/utils/getFontSize';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';
import { parseVerseId } from '@/utils/bibleUtils/utils';
import {useSetNavigationOptions} from '@/hooks/useSetNavigationOptions';

interface RouteParams {
  verseId: number;
  bookName: string;
  chapterNumber: number;
  verseNumber: number;
}

interface NoteItem {
  title: string;
  content: string;
  createdAt?: string;
}

export default function BibleNoteScreen() {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const { verseId, bookName, chapterNumber, verseNumber } = route.params as RouteParams;
  const [notes, setNotes] = useState<NoteItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingContent, setEditingContent] = useState('');

  useEffect(() => {
    loadNotes();
  }, [verseId]);

  useSetNavigationOptions({
    title: '笔记'
  });

  const loadNotes = async () => {
    try {
      const { bookId, chapter } = parseVerseId(verseId);
      const storageKey = `bible.notes.${bookId}.${chapter}`;
      const notesData = await getObjectAsync(storageKey) || {};

      // 获取当前verseId对应的笔记数组
      const verseNotes = notesData[verseId];
      if (verseNotes && Array.isArray(verseNotes)) {
        // 按创建时间排序，最新的在前
        const sortedNotes = verseNotes.sort((a, b) => {
          if (a.createdAt && b.createdAt) {
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          }
          return 0;
        });
        setNotes(sortedNotes);
      } else if (verseNotes && verseNotes.title && verseNotes.content) {
        // 兼容旧的单个笔记格式，转换为数组
        setNotes([verseNotes]);
      } else {
        setNotes([]);
      }
    } catch (error) {
      console.error('加载笔记失败:', error);
      setNotes([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteNote = async (noteIndex: number) => {
    Alert.alert(
      '删除笔记',
      '确定要删除这条笔记吗？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              const { bookId, chapter } = parseVerseId(verseId);
              const storageKey = `bible.notes.${bookId}.${chapter}`;
              const notesData = await getObjectAsync(storageKey) || {};

              // 获取当前verseId的笔记数组
              const verseNotes = notesData[verseId];
              if (verseNotes && Array.isArray(verseNotes)) {
                // 删除指定索引的笔记
                verseNotes.splice(noteIndex, 1);

                // 如果数组为空，删除整个verseId键
                if (verseNotes.length === 0) {
                  delete notesData[verseId];
                } else {
                  notesData[verseId] = verseNotes;
                }
              }

              // 更新存储
              await setObjectAsync(storageKey, notesData);

              // 重新加载笔记
              await loadNotes();
              // 发送事件通知刷新笔记数据
              if (globalThis.eventEmitter && globalThis.eventEmitter.emit) {
                globalThis.eventEmitter.emit('BibleNotesUpdated');
              }
            } catch (error) {
              console.error('删除笔记失败:', error);
            }
          }
        }
      ]
    );
  };

  const handleCreateNote = () => {
    navigation.navigate('BibleNoteCreate' as never, {
      verseIds: [verseId],
      bookName,
      verseRange: `${chapterNumber}:${verseNumber}`
    } as never);
  };

  const handleEditNote = (index: number) => {
    setEditingIndex(index);
    setEditingContent(notes[index]?.content || '');
  };

  const handleSaveEdit = async () => {
    if (editingIndex === null || !editingContent.trim()) {
      return;
    }

    try {
      const { bookId, chapter } = parseVerseId(verseId);
      const storageKey = `bible.notes.${bookId}.${chapter}`;
      const notesData = await getObjectAsync(storageKey) || {};

      // 获取当前verseId的笔记数组
      const verseNotes = notesData[verseId];
      if (verseNotes && Array.isArray(verseNotes)) {
        // 更新指定索引的笔记内容
        verseNotes[editingIndex] = {
          ...verseNotes[editingIndex],
          content: editingContent.trim()
        };
        notesData[verseId] = verseNotes;

        // 更新存储
        await setObjectAsync(storageKey, notesData);

        // 重新加载笔记
        await loadNotes();
        // 发送事件通知刷新笔记数据
        if (globalThis.eventEmitter && globalThis.eventEmitter.emit) {
          globalThis.eventEmitter.emit('BibleNotesUpdated');
        }
      }

      // 退出编辑模式
      setEditingIndex(null);
      setEditingContent('');
    } catch (error) {
      console.error('保存笔记失败:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingIndex(null);
    setEditingContent('');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {notes.length === 0 ? (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>暂无笔记</Text>
            <TouchableOpacity style={styles.createButton} onPress={handleCreateNote}>
              <Text style={styles.createButtonText}>创建笔记</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            {notes.map((note, index) => (
              <View key={index} style={styles.noteItemContainer}>
                <View style={styles.noteItem}>
                  <View style={styles.noteHeader}>
                    <Text style={styles.noteTitle}>{note.title}</Text>
                    {note.createdAt && (
                      <Text style={styles.noteDate}>
                        {new Date(note.createdAt).toLocaleString('zh-CN', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Text>
                    )}
                  </View>

                  {editingIndex === index ? (
                    <TextInput
                      style={styles.editInput}
                      value={editingContent}
                      onChangeText={setEditingContent}
                      multiline
                      numberOfLines={4}
                      textAlignVertical='top'
                      autoFocus
                    />
                  ) : (
                    <Text style={styles.noteContent}>{note.content}</Text>
                  )}
                </View>

                {/* 右下角按钮组 */}
                <View style={styles.actionButtons}>
                  {editingIndex === index ? (
                    <>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={handleCancelEdit}
                      >
                        <Image
                          source={require('@/assets/images/close.png')}
                          style={styles.buttonIcon}
                        />
                        <Text style={styles.cancelButtonText}>取消</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={handleSaveEdit}
                      >
                        <Image
                          source={require('@/assets/images/ok.png')}
                          style={styles.buttonIcon}
                        />
                        <Text style={styles.saveButtonText}>保存</Text>
                      </TouchableOpacity>
                    </>
                  ) : (
                    <>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleEditNote(index)}
                      >
                        <Image
                          source={require('@/assets/images/note.png')}
                          style={styles.buttonIcon}
                        />
                        <Text style={styles.editButtonText}>编辑</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleDeleteNote(index)}
                      >
                        <Image
                          source={require('@/assets/images/close.png')}
                          style={styles.buttonIcon}
                        />
                        <Text style={styles.deleteButtonText}>删除</Text>
                      </TouchableOpacity>
                    </>
                  )}
                </View>
              </View>
            ))}
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

BibleNoteScreen.navigationOptions = () => ({
  title: '经文笔记',
  headerLeft: () => (
    <OkButton
      onPress={() => {
        if (globalThis.eventEmitter && globalThis.eventEmitter.emit) {
          globalThis.eventEmitter.emit('BibleNoteGoBack');
        }
      }}
    />
  )
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff'
  },
  content: {
    flex: 1,
    padding: 16
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    fontSize: getFontSize().mediumFontSize,
    color: '#666'
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100
  },
  emptyText: {
    fontSize: getFontSize().mediumFontSize,
    color: '#666',
    marginBottom: 20
  },
  createButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8
  },
  createButtonText: {
    color: '#fff',
    fontSize: getFontSize().mediumFontSize,
    fontWeight: 'bold'
  },
  noteItemContainer: {
    marginBottom: 16,
    gap: 8
  },
  noteItem: {
    borderRadius: 8,
  },
  noteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8
  },
  noteTitle: {
    fontSize: getFontSize().mediumFontSize,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    marginRight: 8
  },
  noteDate: {
    fontSize: getFontSize().smallFontSize,
    color: '#999'
  },
  editInput: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 8,
    fontSize: getFontSize().mediumFontSize,
    lineHeight: getFontSize().mediumFontSize + 4,
    color: '#333',
    minHeight: 80,
    textAlignVertical: 'top'
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: 60,
    justifyContent: 'center',
    gap: 4
  },
  buttonIcon: {
    width: 16,
    height: 16
  },
  editButtonText: {
    color: '#333',
    fontSize: getFontSize().smallFontSize,
    fontWeight: '500'
  },
  deleteButtonText: {
    color: '#333',
    fontSize: getFontSize().smallFontSize,
    fontWeight: '500'
  },
  saveButtonText: {
    color: '#333',
    fontSize: getFontSize().smallFontSize,
    fontWeight: '500'
  },
  cancelButtonText: {
    color: '#333',
    fontSize: getFontSize().smallFontSize,
    fontWeight: '500'
  },
  noteContent: {
    fontSize: getFontSize().mediumFontSize,
    color: '#666',
    lineHeight: getFontSize().mediumFontSize + 4,
    minHeight: (getFontSize().mediumFontSize + 4) * 4,
    backgroundColor: '#f8f8f8',
    padding: 8,
    borderRadius: 4
  }
});

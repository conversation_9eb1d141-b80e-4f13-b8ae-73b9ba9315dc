import { MomentItemProps, MomentScreenContext } from '@/screens/MomentsScreen/utils';
import React, { useContext } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import { Image } from 'expo-image';
import { RedDot } from '@/components';
import { getCurrentUser } from '@/utils/user';
import { useNavigation } from '@react-navigation/native';

interface MomentArticleProps {
  item: MomentItemProps;
}

/* eslint-disable @typescript-eslint/no-explicit-any */
const MomentArticle: React.FC<MomentArticleProps> = ({ item }) => {
  const { shareMoment, avatarSize, avatarMarginRight } = useContext(MomentScreenContext);
  const { setMomentClicked } = useContext(AppContext) as any;
  const { navigate } = useNavigation<any>();
  const openMoment = () => {
    globalThis.dsObject.clickMoment(item.id);
    // 判断是否为节目详情分享链接
    const url = item.url || '';
    const match = url.match(/programDetail\?channelId=([^&]+)&programId=([^&]+)/);

    console.log('match', item.url, match);
    if (match) {
      const channelId = match[1];
      const programId = match[2];
      navigate('ChannelProgram', { channelId, programId });
    } else {
      navigate('WebApp', { url: item.url, enableShare: true });
    }
    // set local iClicked = true
    setMomentClicked(item.id);
  };

  return (
    <TouchableOpacity
      style={{ marginLeft: avatarSize + avatarMarginRight }}
      activeOpacity={1}
      onPress={() => {
        openMoment();
      }}
      onLongPress={() => {
        shareMoment?.(item.url);
      }}>
      <View
        style={{
          flexDirection: 'row',
          backgroundColor: Colors.lightBlue
        }}>
        <View style={{ padding: 0.5, backgroundColor: '#CCCCCC' }}>
          <Image
            style={{
              height: 100,
              width: 100,
              backgroundColor: Colors.buttonBackground
            }}
            transition={100}
            cachePolicy={'memory-disk'}
            contentFit={'fill'}
            source={{
              uri: item.image
            }}
          />
        </View>
        <Text
          style={{
            fontSize: getCurrentUser().getSmallFontSize(),
            flex: 1,
            paddingVertical: 7,
            paddingHorizontal: 8,
            height: 100
          }}>
          {item.title.trim()}
        </Text>
      </View>
      {!item.iClicked ? <RedDot right={1} top={8} /> : null}
    </TouchableOpacity>
  );
};

export default MomentArticle;

import {
  FmBottomTabNavigator,
  fmDefaultTheme,
  fmScreenOptionsLoggedIn,
  fmScreenOptionsNotLoggedIn
} from '@/fishMeet/navigation/FmBottomTabNavigator';
import { NativeStackNavigationOptions, createNativeStackNavigator } from '@react-navigation/native-stack';
import React, { useState } from 'react';

import AcceptContactCardScreen from '@/screens/AcceptContactCardScreen';
import AccountDoneScreen from '@/screens/AccountDoneScreen';
import AddFriendScreen from '@/screens/AddFriendScreen';
import AdvancedSettingsScreen from '@/screens/AdvancedSettingsScreen';
import AttendanceHomeScreen from '@/screens/AttendanceHomeScreen';
import AttendanceRecordScreen from '@/screens/AttendanceRecordScreen';
import { BackButton } from '@/components/navigation/NavigationButtons';
import BibleScreen from '@/screens/BibleScreen';
import BibleSelectScreen from '@/screens/BibleSelectScreen';
import ChatScreen from '@/screens/ChatScreen';
import CreateAccountScreen from '@/screens/CreateAccountScreen';
import HelpMeScreen from '@/screens/HelpMeScreen';
import CreateMomentScreen from 'src/screens/CreateMomentScreen';
import CreateStudyGroupScreen from '@/screens/CreateStudyGroupScreen/CreateStudyGroupScreen';
import DebugScreen from '@/screens/DebugScreen';
import ExportScreen from '@/screens/ExportScreen';
import ForgotPasswordScreen from '@/screens/ForgotPasswordScreen';
import GroupMemberSelectScreen from '@/screens/GroupMemberSelectScreen';
import GroupProgressListScreen from '@/screens/GroupProgressListScreen';
import GroupProgressScreen from '@/screens/GroupProgressScreen';
import GroupScreen from '@/screens/GroupScreen';
import { IDigestBottomTabNavigator } from './BottomTabNavigator';
import JoinStudyGroupScreen from '@/screens/JoinStudyGroupScreen';
import LessonScreen from '@/screens/LessonScreen';
import LessonTableOfContentScreen from '@/screens/LessonTableOfContentScreen';
import LoginInfoSettingScreen from '@/fishMeet/screens/LoginInfoSettingScreen';
import MemberInviteScreen from '@/screens/MemberInviteScreen';
import MemberScreen from '@/screens/MemberScreen';
import MultiSelectScreen from '@/screens/MultiSelectScreen';
import MyStudyGroupsScreen from '@/screens/MyStudyGroupsScreen/MyStudyGroupsScreen';
import { NavigationContainer } from '@react-navigation/native';
import NoteScreen from '@/screens/NoteScreen';
import PasswordDoneScreen from '@/screens/PasswordDoneScreen';
import QRScanScreen from '@/screens/QRScanScreen';
import SelectGroupOrUserScreen from '@/screens/SelectGroupOrUserScreen';
import SelectMeetingToolScreen from '@/screens/SelectMeetingToolScreen';
import SelectScreen from '@/screens/SelectScreen';
import SendFeedbackScreen from '@/screens/SendFeedbackScreen';
import SetupProxyScreen from '@/screens/SetupProxyScreen';
import SystemInfoScreen from '@/screens/SystemInfoScreen';
import UpdateNameScreen from '@/screens/UpdateNameScreen';
import MyInfoScreen from '@/screens/MyInfoScreen';
import UpdatePasswordScreen from '@/screens/UpdatePasswordScreen';
import UserDeleteScreen from '@/screens/UserDeleteScreen';
import UserLoginScreen from '@/screens/UserLoginScreen';
import WebAppScreen from '@/screens/WebAppScreen';
import ChannelProgramScreen from '@/screens/ChannelProgramScreen';
import ChannelDetailScreen from '@/screens/ChannelDetailScreen';
import MyStudyClassListScreen from '@/screens/MyStudyClassListScreen';
import MyChannelListScreen from '@/screens/MyChannelListScreen';
import ChannelProgramsScreen from '@/screens/ChannelProgramsScreen';
import BibleBooksScreen from 'src/screens/BibleBooksScreen';
import BibleChapterScreen from 'src/screens/BibleChapterScreen';
import BibleSearchScreen from 'src/screens/BibleSearchScreen';
import BibleSettingScreen from 'src/screens/BibleSettingScreen';
import BibleNoteCreateScreen from 'src/screens/BibleNoteCreateScreen';
import BibleNoteScreen from 'src/screens/BibleNoteScreen';
import { getCurrentUser } from '@/utils/user';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { useColorScheme } from 'react-native';
import { useEventListener } from 'expo';

// let __bottomOffset = 0;
// if (Platform.OS === 'ios') {
//   // Workaround: Special handling for iPad with FaceId, for some reason, the old 4.x react-navigation doesn't work well on iPad
//   // iPad Pro 12.9-inch (4th generation) - iPad8,12
//   // iPad Pro 12.9-inch (3rd generation)
//   // iPad Pro 11-inch (2nd generation) - iPad8,9
//   // iPad Pro 11-inch
//   // iPad Air (4th) - iPad13,2

//   // Device.modelId is only available for iOS
//   const modelId = Device.modelId;
//   if (modelId.startsWith('iPad')) {
//     const major = parseInt(modelId.substr(4));
//     // iPad (8th) - iPad11,7 (no offset)
//     // iPad Pro 9.7inch - iPad6,4 (no offset)
//     if ([6, 11].indexOf(major) === -1) {
//       __bottomOffset = 20;
//     }
//   }
// }

const DefaultTheme = isFishMeet
  ? fmDefaultTheme
  : {
      dark: false,
      colors: {
        primary: 'black',
        background: 'white',
        card: 'white',
        text: 'black',
        border: 'black',
        notification: 'red'
      }
    };

const DarkTheme = DefaultTheme;

type AppNavigatorProps = {
  onReady: React.ComponentProps<typeof NavigationContainer>['onReady'];
};

export function AppNavigator({ onReady }: AppNavigatorProps) {
  const colorScheme = useColorScheme();
  return (
    <NavigationContainer theme={colorScheme === 'dark' ? DarkTheme : DefaultTheme} onReady={onReady}>
      <RootNavigator />
    </NavigationContainer>
  );
}

function RootNavigator() {
  const [isLoggedIn, setIsLoggedIn] = useState(getCurrentUser().isLoggedOn());

  useEventListener(globalThis.eventEmitter, 'userChanged', ({ reason }) => {
    switch (reason) {
      case 'logout':
        setIsLoggedIn(false);
        break;
      case 'login':
        setIsLoggedIn(true);
        break;
    }
  });

  const screenOptions = {
    headerShadowVisible: true,
    headerTitleAlign: 'center',
    headerBackVisible: false, // This is needed for Android, otherwise it shows two back buttons
    headerLeft: () => <BackButton />
  } satisfies NativeStackNavigationOptions;

  // NOTE: Use the useSetNavigationOptions hook to set navigation options in function component screens.
  // Function component screens should not set options through the options prop because modifying it will not
  // trigger a re-render on the screen to show the new options (such as changing language on UserLoginScreen).

  const Stack = createNativeStackNavigator();

  const loggedInScreens = (
    <Stack.Navigator screenOptions={isFishMeet ? fmScreenOptionsLoggedIn : screenOptions}>
      <Stack.Screen
        name='BottomTab'
        component={isFishMeet ? FmBottomTabNavigator : IDigestBottomTabNavigator}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name='LessonTableOfContent'
        component={LessonTableOfContentScreen}
        options={LessonTableOfContentScreen.navigationOptions}
      />
      <Stack.Screen name='HelpMe' component={HelpMeScreen} />
      <Stack.Screen name='Lesson' component={LessonScreen} options={LessonScreen.navigationOptions} />
      <Stack.Screen name='Chat' component={ChatScreen} options={ChatScreen.navigationOptions} />
      <Stack.Screen name='CreateMoment' component={CreateMomentScreen} />
      <Stack.Screen name='ChannelProgram' component={ChannelProgramScreen} />
      <Stack.Screen name='ChannelDetail' component={ChannelDetailScreen} />
      <Stack.Screen name='MyStudyClassList' component={MyStudyClassListScreen} />
      <Stack.Screen name='MyChannelList' component={MyChannelListScreen} />
      <Stack.Screen name='ChannelPrograms' component={ChannelProgramsScreen} />
      <Stack.Screen name='Group' component={GroupScreen} options={GroupScreen.navigationOptions} />
      <Stack.Screen
        name='GroupMemberSelect'
        component={GroupMemberSelectScreen}
        options={GroupMemberSelectScreen.navigationOptions}
      />
      <Stack.Screen
        name='MyStudyGroups'
        component={MyStudyGroupsScreen}
        options={MyStudyGroupsScreen.navigationOptions}
      />
      <Stack.Screen name='Note' component={NoteScreen} options={NoteScreen.navigationOptions} />
      <Stack.Screen name='Bible' component={BibleScreen} options={BibleScreen.navigationOptions} />
      <Stack.Screen name='BibleSelect' component={BibleSelectScreen} options={BibleSelectScreen.navigationOptions} />
      <Stack.Screen name='BibleSetting' component={BibleSettingScreen} />
      <Stack.Screen name='BibleBooks' component={BibleBooksScreen} />
      <Stack.Screen name='BibleChapter' component={BibleChapterScreen} />
      <Stack.Screen name='BibleSearch' component={BibleSearchScreen} />
      <Stack.Screen name='BibleNote' component={BibleNoteScreen} />
      <Stack.Screen name='BibleNoteCreate' component={BibleNoteCreateScreen} />
      <Stack.Screen name='UpdateName' component={UpdateNameScreen} options={UpdateNameScreen.navigationOptions} />
      <Stack.Screen name='MyInfo' component={MyInfoScreen} />
      <Stack.Screen
        name='UpdatePassword'
        component={UpdatePasswordScreen}
        options={UpdatePasswordScreen.navigationOptions}
      />
      <Stack.Screen name='QRScan' component={QRScanScreen} />
      <Stack.Screen name='Select' component={SelectScreen} />
      <Stack.Screen
        name='SelectMeetingTool'
        component={SelectMeetingToolScreen}
        options={SelectMeetingToolScreen.navigationOptions}
      />
      <Stack.Screen
        name='SelectGroupOrUser'
        component={SelectGroupOrUserScreen}
        options={SelectGroupOrUserScreen.navigationOptions}
      />
      <Stack.Screen name='MultiSelect' component={MultiSelectScreen} options={MultiSelectScreen.navigationOptions} />
      <Stack.Screen name='MemberInvite' component={MemberInviteScreen} />
      <Stack.Screen name='WebApp' component={WebAppScreen} options={WebAppScreen.navigationOptions} />
      <Stack.Screen
        name='GroupProgress'
        component={GroupProgressScreen}
        options={GroupProgressScreen.navigationOptions}
      />
      <Stack.Screen
        name='GroupProgressList'
        component={GroupProgressListScreen}
        options={GroupProgressListScreen.navigationOptions}
      />
      <Stack.Screen name='CreateStudyGroup' component={CreateStudyGroupScreen} />
      <Stack.Screen name='JoinStudyGroup' component={JoinStudyGroupScreen} />
      <Stack.Screen
        name='AttendanceHome'
        component={AttendanceHomeScreen}
        options={AttendanceHomeScreen.navigationOptions}
      />
      <Stack.Screen
        name='AttendanceRecord'
        component={AttendanceRecordScreen}
        options={AttendanceRecordScreen.navigationOptions}
      />
      <Stack.Screen name='Member' component={MemberScreen} />
      <Stack.Screen name='AcceptContactCard' component={AcceptContactCardScreen} />
      <Stack.Screen name='Export' component={ExportScreen} options={ExportScreen.navigationOptions} />
      <Stack.Screen name='AddFriend' component={AddFriendScreen} options={AddFriendScreen.navigationOptions} />
      <Stack.Screen
        name='AdvancedSettings'
        component={AdvancedSettingsScreen}
        options={AdvancedSettingsScreen.navigationOptions}
      />
      {isFishMeet && <Stack.Screen name='LoginInfoSetting' component={LoginInfoSettingScreen} />}
      <Stack.Screen name='Debug' component={DebugScreen} options={DebugScreen.navigationOptions} />
      <Stack.Screen name='SendFeedback' component={SendFeedbackScreen} options={SendFeedbackScreen.navigationOptions} />
      <Stack.Screen name='SystemInfo' component={SystemInfoScreen} options={SystemInfoScreen.navigationOptions} />
      <Stack.Screen name='SetupProxy' component={SetupProxyScreen} options={SetupProxyScreen.navigationOptions} />
      <Stack.Screen name='UserDelete' component={UserDeleteScreen} options={UserDeleteScreen.navigationOptions} />
    </Stack.Navigator>
  );

  const notLoggedInScreens = (
    <Stack.Navigator screenOptions={isFishMeet ? fmScreenOptionsNotLoggedIn : screenOptions}>
      <Stack.Screen name='UserLogin' component={UserLoginScreen} />
      {!isFishMeet && (
        <Stack.Screen name='AccountDone' component={AccountDoneScreen} options={AccountDoneScreen.navigationOptions} />
      )}
      <Stack.Screen
        name='CreateAccount'
        component={CreateAccountScreen}
        options={CreateAccountScreen.navigationOptions}
      />
      <Stack.Screen name='PasswordDone' component={PasswordDoneScreen} options={PasswordDoneScreen.navigationOptions} />
      <Stack.Screen name='Forgot' component={ForgotPasswordScreen} options={ForgotPasswordScreen.navigationOptions} />
      <Stack.Screen name='WebApp' component={WebAppScreen} options={WebAppScreen.navigationOptions} />
      <Stack.Screen name='Select' component={SelectScreen} />
    </Stack.Navigator>
  );

  return isLoggedIn ? loggedInScreens : notLoggedInScreens;
}

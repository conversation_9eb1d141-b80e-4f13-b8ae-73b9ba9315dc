/* eslint-disable @typescript-eslint/no-explicit-any */

import 'reflect-metadata';

import { callWeb<PERSON><PERSON>, callWebServiceAsync, showWebServiceCallErrorsAsync, uploadFileAsync } from '@/dataStorage/storage';
import { getCurrentUser, getHttpsServer } from '@/utils/user';

import { ETranslation } from '@/utils/bibleUtils';
import { dataServices } from '@/ioc/interfaces';
import { injectable } from 'inversify';
import { sortByPinYin } from '@/utils/pinyin';

//////////////////////////
//
// Data Service API
//
// +[home]
//
// +[attendance]
//
// +[chat]
//    |-sendMessage
//    |-getMessages
//    |-sendFile
//
// +[feedback]
//
// +[groups](get)
//    |- ${groupId}/${dateString} (DELETE,PUT)
//
// +[group]
//    |- createDiscussion
//    |- ${groupId}
//       |- ${classId} (DELETE, PUT)
//       |- addUser
//          |- ${email}
//       |- accept
//       |- create
//       |- createOneOnOne
//       |- leave
//       |- meetingLink
//       |- removeUser
//       |- reject
//       |- studyProgress
//       |- update
//       |- userHideMessages
//       |- {userId}
//          |- demote
//          |- promote
//
// +[lesson]
//    |- ${lessonId}
//    |- ${lesson.name}?templateVersion=${version}`)
//    |- shareLesson
//
// +[moments]
//    |- ${momentsId}
//    |- click
//       |- ${momentsId}
//    |- from=<>&to=<>
//    |- like
//       |- ${momentsId}
//    |- unlike
//       |- ${momentsId}
//    |- seen
//       |- ${momentsId}
//    |- linkPreview?url=${url}
//
// +[user]
//    |- avatars
//    |- collection
//       |- ${id}
//    |- contact
//       |- ${userId}
//    |- create
//    |- data
//       |- chat
//       |- toplist(GET,PUT)
//    |- delete
//    |- getUpdate
//    |- invite
//    |- inviteUsers
//    |- lastSeenClass
//    |- launchUrl
//    |- login
//    |- profile
//    |- refreshAccessToken
//    |- registerDevice
//    |- resetPassword
//    |- tag
//       |- ${messageId}/${tag}
//    |- updateProfile
//    |- unregisterDevice
//
//////////////////////////

@injectable()
class iDigestDS implements dataServices {
  lastHttpErrorTime: Date | null;
  public isSwitchingToBackup: boolean;

  constructor() {
    this.lastHttpErrorTime = null;
    this.isSwitchingToBackup = false;
  }

  public getHttpsServer(url: string) {
    const base = getHttpsServer();
    if (!url) {
      return base;
    }

    if (url.startsWith('/')) {
      return base + url;
    }

    return `${base}/${url}`;
  }

  public async switchToBackupServer() {
    console.log('we are trying to fetch backup server');
    this.isSwitchingToBackup = true;

    try {
      const backupServer = await this.getBackupHttpsServer();
      console.log(`backup server is ${backupServer}`);
      if (!backupServer) {
        console.error('fetch backup server failed!');
      } else {
        const servers = backupServer.split('.');
        if (servers.length < 2) {
          const decodedBackupServer = atob(backupServer);
          console.log(`decoded backup server is:${decodedBackupServer}`);
          getCurrentUser().setUpHttpsServer(decodedBackupServer, true);
        } else {
          const decodedBackupServer = atob(servers[0]);
          console.log(`decoded backup server is:${decodedBackupServer}`);
          getCurrentUser().setUpHttpsServer(decodedBackupServer);

          const decodedCryptoProxy = atob(servers[1]);
          console.log(`decoded cryptoproxy server is:${decodedCryptoProxy}`);
          getCurrentUser().setCryptoProxy(decodedCryptoProxy, true);
        }
        console.log(`server now is ${getHttpsServer()}`);
      }
    } finally {
      this.isSwitchingToBackup = false; // always reset the state after this function is done
      console.log('exit switching to backup server');
    }
  }

  public setLastHttpErrorTime(errorTime: Date | null) {
    this.lastHttpErrorTime = errorTime;
  }

  public getLastHttpErrorTime() {
    return this.lastHttpErrorTime;
  }

  public async getBackupHttpsServer() {
    const encodedCurrentUrl = btoa(getHttpsServer());
    console.log(`current server is ${getHttpsServer()}, encoded server is ${encodedCurrentUrl}`);
    //older version before 2/27/2025 use /bs/ path.
    //const result = await callWebApi(`http://************/bs/?from=${encodedCurrentUrl}`);
    //use new path, the new path can return more data without breaking older version Apps
    const result = await callWebApi(`http://************/as/?from=${encodedCurrentUrl}`);
    if (result) {
      return result.body;
    }
    return null;
  }

  //////////////////////////
  //
  // +[home] data service
  //
  //////////////////////////

  async checkHttpsServer(httpsServer: string) {
    return await callWebApi(`${httpsServer}/home?verifyProxy=1`);
  }

  async getCountryFromIP(): Promise<string> {
    const result = await callWebApi(this.getHttpsServer('checkip'));
    return result?.body.country;
  }

  //////////////////////////
  //
  // +[attendance]
  //
  //////////////////////////

  async getAttendance(groupId: number) {
    return await callWebApi(this.getHttpsServer(`attendance/${groupId}`));
  }

  async deleteAttendance(groupId: number, dateString: string) {
    return await callWebApi(this.getHttpsServer(`attendance/${groupId}/${dateString}`), 'DELETE');
  }

  async setAttendance(groupId: number, dateString: string, body: any) {
    return await callWebApi(this.getHttpsServer(`attendance/${groupId}/${dateString}`), 'PUT', 200, body);
  }

  //////////////////////////
  //
  // +[user]
  //
  //////////////////////////

  //
  // [in]
  //   users: userId1, userId2, ...
  //
  // [out]
  //   http response
  //
  // [Example]
  //   [in]
  //     Http request: POST https://s1.mycbsf.org/user/avatars {"users":[376,16023,45,1005,697,212,882]}
  //   [out]
  //     Http response:
  //       [Header]
  //       [Body]
  //        "376": <avatar>,
  //        :16023":<avatar>,
  //        ...
  //       [Status] 200
  //
  public async getUserAvatars(users: any) {
    return await callWebApi(this.getHttpsServer('user/avatars'), 'POST', 200, { users } as any);
  }

  public async setUserAvatars(localUri: string) {
    const uploadResult = await uploadFileAsync(this.getHttpsServer('user/avatar'), localUri, 'PUT', 'userAvatar');
    return await showWebServiceCallErrorsAsync(uploadResult, 201);
  }

  public async loginUser(loginId: string, password: string) {
    const body = { loginId, password };
    return await callWebApi(this.getHttpsServer('user/login'), 'POST', 200, body as any);
  }

  async createUser(body: any) {
    return await callWebApi(this.getHttpsServer('user/create'), 'POST', 201, body);
  }

  async deleteCollection(chatId: number, source: any, messageId: number) {
    return await callWebApi(this.getHttpsServer(`user/collection/${chatId}/${source}/${messageId}`), 'DELETE');
  }

  async setCollection(chatId: number, target: any, body: any) {
    return await callWebApi(this.getHttpsServer(`user/collection/${chatId}/${target}`), 'POST', 200, body);
  }

  async getCollectionCategory(chatId: number, category: string) {
    return await callWebApi(this.getHttpsServer(`user/collection/${chatId}/${category}`));
  }

  async getGroupViewTopList() {
    // if 'user/data/toplist' for the user not set yet(not configured before), then server would return 404 which needs to skip
    return await callWebApi(this.getHttpsServer('user/data/toplist'), 'GET', [200, 404], undefined);
  }

  async setGroupViewTopList(body: number[]) {
    return await callWebApi(this.getHttpsServer('user/data/toplist'), 'PUT', 200, body);
  }

  async getPreMeetingData(groupId: number) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}`), 'GET', 200);
  }

  async setPreMeetingData(groupId: number, meetingData: any) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/update`), 'POST', 200, meetingData);
  }

  async resetPassword(body: any) {
    return await callWebApi(this.getHttpsServer('user/resetPassword'), 'PUT', 200, body);
  }

  async setUserLastSeenClass(latestClass: number) {
    return await callWebApi(this.getHttpsServer(`user/lastSeenClass/${latestClass}`), 'PUT');
  }

  async getUserProfile() {
    return await callWebApi(this.getHttpsServer('user/profile'));
  }

  async setUserProfile(userProfile: any) {
    return await callWebApi(this.getHttpsServer('user/profile'), 'PUT', 200, userProfile);
  }

  async setLaunchUrl(launchUrl: any) {
    return await callWebServiceAsync(this.getHttpsServer('user/launchUrl'), 'POST', { launchUrl } as any);
  }

  async joinGroupViaCode(joinCode: string) {
    return await callWebServiceAsync(this.getHttpsServer('group/join'), 'POST', { joinCode } as any);
  }

  async refreshAccessToken() {
    return await callWebApi(this.getHttpsServer('user/refreshAccessToken'), 'POST');
  }

  async checkUserUpdate(version: number) {
    return await callWebApi(this.getHttpsServer(`user/getUpdate?templateVersion=${version}`));
  }

  async deleteMessage(messageId: number) {
    return await callWebServiceAsync(this.getHttpsServer(`user/message/${messageId}`), 'DELETE');
  }

  async deleteUser() {
    return await callWebApi(this.getHttpsServer('user/delete'), 'DELETE');
  }

  async updateUserProfile(body: any) {
    return await callWebApi(this.getHttpsServer('user/updateProfile'), 'PUT', 200, body as any);
  }

  async getUserData(dataId: number | string) {
    return await callWebApi(this.getHttpsServer(`user/data/${dataId}`));
  }

  async setUserData(dataId: number | string, body: any) {
    return await callWebApi(this.getHttpsServer(`user/data/${dataId}`), 'PUT', 200, body as any);
  }

  async getInviteUsers(hideUserId?: number) {
    const result = await callWebApi(this.getHttpsServer('user/inviteUsers'));
    if (!result || !Array.isArray(result.body)) {
      return null;
    }

    let inviteUsers = sortByPinYin(result.body, (item) => item.displayName);

    const contactInfo = await this.getAllContactAsync();
    const blockedUsers = new Set([
      ...contactInfo.me.filter((u: any) => u.block).map((u: any) => u.userId),
      ...contactInfo.blockedBy
    ]);

    // remove users blocked by me
    const blockedByMe = contactInfo.me.filter((u: any) => u.block).map((u: any) => u.userId);
    if (blockedByMe.length > 0) {
      inviteUsers = inviteUsers.filter((it) => blockedByMe.indexOf(it.userId) === -1);
    }

    if (!hideUserId) {
      return inviteUsers;
    }

    const newTags = contactInfo.me.reduce((tags: any, contact: any) => {
      tags[contact.userId] = contact.tag;
      return tags;
    }, {});

    return inviteUsers.reduce((result, it) => {
      if (!blockedUsers.has(it.userId) && it.userId !== hideUserId) {
        result.push({
          id: it.loginId,
          userId: it.userId,
          uniqueId: it.loginId,
          text: `${it.displayName} ${newTags[it.userId] ? newTags[it.userId] + ' ' : ''}(${it.loginId})`,
          isSelected: false,
          displayName: it.displayName
        });
      }
      return result;
    }, []);
  }

  async inviteUser(email: string, greetings: string) {
    return await callWebApi(this.getHttpsServer('user/invite'), 'POST', 200, { email, greetings } as any);
  }

  async registerDevice(token: any) {
    return await callWebApi(this.getHttpsServer('user/registerDevice'), 'POST', 200, { token } as any);
  }

  async unregisterDevice(token: any) {
    return await callWebApi(this.getHttpsServer('user/unregisterDevice'), 'POST', 200, { token } as any);
  }

  async saveAnswer(classId: any, sessionIndex: any, body: any) {
    return await callWebApi(
      this.getHttpsServer(`user/data/answer,${classId},${sessionIndex}`),
      'PUT',
      200,
      body,
      false
    );
  }

  async setUserTagOrBlock(userId: number, tag?: string, block?: number) {
    const url = this.getHttpsServer(`/user/contact/${userId}`);
    const body: { tag?: string; block?: number } = {};

    body.tag = tag !== undefined ? tag : '';
    body.block = block !== undefined ? block : 0;
    try {
      const response = await callWebServiceAsync(url, 'PUT', body as any);
      return response;
    } catch (error) {
      console.error('Error during set user contact:', error);
    }
  }

  async fetchUserTagAndBlock(userId: number) {
    const url = this.getHttpsServer(`/user/contact/${userId}`);
    try {
      const response = await callWebServiceAsync(url, 'GET');
      if (response.status === 200) {
        return {
          tag: response.body.tag || '',
          block: response.body.block || 0
        };
      } else if (response.status === 404) {
        return { tag: '', block: 0 };
      } else {
        console.error('Failed to fetch user tag and block:', response);
        return { tag: '', block: 0 };
      }
    } catch (error) {
      console.error('Error during fetch user tag and block:', error);
      return { tag: '', block: 0 };
    }
  }

  async getAllContactAsync() {
    const url = this.getHttpsServer('/user/contact');
    try {
      const response = await callWebServiceAsync(url, 'GET');
      if (response.status === 200 && response.body) {
        const me = response.body.me || [];
        const blockedBy = response.body.blockedBy || [];

        return { me, blockedBy };
      } else {
        console.error('Failed to fetch all user tags:', response);
        return { me: [], blockedBy: [] };
      }
    } catch (error) {
      console.error('Error during fetch all user tags:', error);
      return { me: [], blockedBy: [] };
    }
  }

  //////////////////////////
  //
  // +[groups] data service
  //
  //////////////////////////

  async getGroups() {
    return await callWebApi(this.getHttpsServer('groups'));
  }

  //////////////////////////
  //
  // +[group] data service
  //
  //////////////////////////

  async createDiscussion(classId: number, week: number, questionId: number) {
    return await callWebApi(this.getHttpsServer('group/createDiscussion'), 'POST', 200, {
      classId,
      week,
      questionId
    } as any);
  }

  async createOneOnOne(friendId: any) {
    return await callWebApi(this.getHttpsServer('group/createOneOnOne'), 'POST', 200, { friendId } as any);
  }

  async getGroup(groupId: number) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}`));
  }

  async deleteGroup(groupId: number, showUI?: boolean, onDismissUI?: undefined) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}`), 'DELETE', 200, undefined, showUI, onDismissUI);
  }

  async leaveGroup(groupId: number, showUI?: boolean, onDismissUI?: undefined) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/leave`), 'POST', 200, undefined, showUI, onDismissUI);
  }

  async createGroup(groupId: number, body: any) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/create`), 'POST', 200, body);
  }

  async updateGroup(groupId: number, body: any) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/update`), 'POST', 200, body);
  }

  async removeUserFromGroup(groupId: number, userId: number) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/removeUser/${userId}`), 'POST');
  }

  async getStudyProgress(groupId: number) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/studyProgress`));
  }

  async getClassStudyProgress(groupId: number, classId: number) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/${classId}/studyProgress`));
  }

  async addUserEmail(groupId: number, email: any, isOrgInvite: boolean) {
    let url = this.getHttpsServer(`group/${groupId}/addUser/${email}`);
    if (isOrgInvite) {
      url += '?addOrgUser=1';
    }
    return await callWebApi(url, 'POST', 201);
  }

  async respondToRequest(groupId: number, requestUserId: number, accept: boolean) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/${accept ? 'accept' : 'reject'}`), 'POST', 200, {
      requestUserId
    } as any);
  }

  async userHideMessages(groupId: number, hideMessages: any) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/userHideMessages/${hideMessages}`), 'PUT');
  }

  //
  // [in]
  //   groupId: meeting group id
  //
  // [out]
  //   http response
  //
  // [Example]
  //   [in] groupId: 4915
  //     Http request: GET https://s1.mycbsf.org/group/4915/meetingLink
  //   [out]
  //     Http response:
  //       [Header]
  //       [Body]
  //        "domain": "m.mycbsf.org",
  //        "hash": <hash>,
  //        "jwt": <jwt>,
  //        "room": "idigest-71794",
  //        "server": 2,
  //        "url": "https://m.mycbsf.org/idigest-71794?lang=zhCN&jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjb250ZXh0Ijp7InVzZXIiOnsiaWQiOjM3NiwibmFtZSI6Iua1t-WzsCBGcmFuayBaIn19LCJhdWQiOiJqaXRzaSIsImlzcyI6ImlkaWdlc3QiLCJzdWIiOiJtZWV0LmppdHNpIiwicm9vbSI6ImlkaWdlc3QtNzE3OTQiLCJpYXQiOjE3MTE0MjM5NzcsImV4cCI6MTcxMTQzODM3N30.KYh52XE6-t9QG-aKDvD0eUKE4523wAbak3dRTxA_qdU#config.disableDeepLinking=true&config.startWithAudioMuted=false&config.startWithVideoMuted=true&config.hideLobbyButton=true&config.enableWelcomePage=false&config.prejoinConfig=%7B%22enabled%22%3Afalse%7D&interfaceConfig.filmStripOnly=false&interfaceConfig.TOOLBAR_ALWAYS_VISIBLE=true&appData.localStorageContent=null&config.disableReactions=true"
  //       [Status] 200
  //
  async getMeetingLink(groupId: number) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/meetingLink`));
  }

  async groupUserDemote(groupId: number, userId: number, showUI?: boolean, onDismissUI?: undefined) {
    return await callWebApi(
      this.getHttpsServer(`group/${groupId}/${userId}/demote`),
      'POST',
      200,
      undefined,
      showUI,
      onDismissUI
    );
  }

  async groupUserPromote(groupId: number, userId: number, showUI?: boolean, onDismissUI?: undefined) {
    return await callWebApi(
      this.getHttpsServer(`group/${groupId}/${userId}/promote`),
      'POST',
      200,
      undefined,
      showUI,
      onDismissUI
    );
  }

  async shareGroupLesson(groupId: number, classId: number) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/${classId}`), 'PUT', 200, undefined);
  }

  async deleteGroupLesson(groupId: number, classId: number) {
    return await callWebApi(this.getHttpsServer(`group/${groupId}/${classId}`), 'DELETE');
  }

  //////////////////////////
  //
  // +[moments] data service
  //
  //////////////////////////

  async clickMoment(momentsId: number) {
    return await callWebServiceAsync(this.getHttpsServer(`moments/click/${momentsId}`), 'POST');
  }

  async loadMoment(momentsId: number) {
    return await callWebApi(this.getHttpsServer(`moments?from=${momentsId + 1}&to=${momentsId}`));
  }

  async deleteMoment(momentsId: number) {
    return await callWebApi(this.getHttpsServer(`moments/${momentsId}`), 'DELETE');
  }

  async likeMoment(momentsId: number) {
    return await callWebApi(this.getHttpsServer(`moments/like/${momentsId}`), 'POST');
  }

  async unlikeMoment(momentsId: number) {
    return await callWebApi(this.getHttpsServer(`moments/unlike/${momentsId}`), 'POST');
  }

  async setSeenMoment(momentsId: number) {
    return await callWebApi(this.getHttpsServer(`moments/seen/${momentsId}`), 'POST');
  }

  async loadMomentFrom(momentsCursor: number) {
    const query = momentsCursor === -1 ? '' : '?from=' + momentsCursor;
    return await callWebApi(this.getHttpsServer(`moments${query}`));
  }

  async createMoment(body: any) {
    return await callWebApi(this.getHttpsServer('moments'), 'POST', 201, body);
  }

  async getMomentPreview(url: string) {
    return await callWebApi(this.getHttpsServer(`moments/linkPreview?url=${url}`));
  }

  async getMoments({
    includeGroups,
    groupId,
    lastId
  }: {
    includeGroups?: boolean;
    groupId?: string;
    lastId?: number | undefined;
  }) {
    const params: Record<string, string> = {};

    if (includeGroups) {
      params['showGroups'] = '1';
    }

    if (groupId) {
      params['groups'] = groupId;
    }

    if (lastId && lastId > -1) {
      params['from'] = lastId.toString();
    }

    const queryString = new URLSearchParams(params).toString();
    const url = `moments${queryString ? `?${queryString}` : ''}`;

    return await callWebApi(this.getHttpsServer(url));
  }

  async uploadMoments(loadUri: string) {
    return await uploadFileAsync(this.getHttpsServer('moments/uploadFile'), loadUri, 'POST', 'momentsFile');
  }

  //////////////////////////
  //
  // [chat] data service
  //
  //////////////////////////

  async sendMessage(body: any) {
    return await callWebApi(this.getHttpsServer('chat/sendMessage'), 'POST', 200, body);
  }

  async getChatMessage(chatId: number, queryStr: string) {
    return await callWebApi(this.getHttpsServer(`chat/getMessages/${chatId}?${queryStr}`));
  }

  async deleteChatMessage(chatId: number, messageId: number) {
    return await callWebApi(this.getHttpsServer(`chat/${chatId}/${messageId}`), 'DELETE');
  }

  async getChatTagMessage(messageId: number, tag: string) {
    return await callWebApi(this.getHttpsServer(`chat/tag/${messageId}/${tag}`), 'POST');
  }

  async setChatTagMessage(messageId: number, tag: string) {
    return await callWebApi(this.getHttpsServer(`chat/tag/${messageId}/${tag}`), 'POST');
  }

  async deleteChatTagMessage(messageId: number, tag: string) {
    return await callWebApi(this.getHttpsServer(`chat/tag/${messageId}/${tag}`), 'DELETE');
  }

  async sendChatFileMessage(localUri: any, queryStr: string) {
    const result = await uploadFileAsync(this.getHttpsServer('chat/sendFileMessage') + '?' + queryStr, localUri);
    return await showWebServiceCallErrorsAsync(result, 201);
  }

  //////////////////////////
  //
  // +[feedback] data service
  //
  //////////////////////////

  async postFeedback(body: any) {
    return await callWebApi(this.getHttpsServer('feedback'), 'POST', 201, body as any);
  }

  //////////////////////////
  //
  // +[lesson] data service
  //
  //////////////////////////

  async loadLesson(lessonId: any, lessonFile: any, sessionIndex: any) {
    return await callWebApi(this.getHttpsServer(`lesson/${lessonId}/-/${lessonFile}?index=${sessionIndex}`));
  }

  async getLesson(lessonName: string, version: any) {
    return await callWebApi(this.getHttpsServer(`lesson/${lessonName}?templateVersion=${version}`));
  }

  async getClassCreateGuidePreview() {
    return await callWebApi(this.getHttpsServer('preview/120/4'));
  }

  async shareUserLesson(classId: number, userId: number) {
    return await callWebApi(this.getHttpsServer(`lesson/share/${classId}/${userId}`), 'PUT', 200, undefined);
  }

  async deleteUserLesson(classId: number, userId: number) {
    return await callWebApi(this.getHttpsServer(`lesson/share/${classId}/${userId}`), 'DELETE');
  }
  //////////////////////////
  //
  // +[channels] data service
  //
  //////////////////////////
  async getChannelOwned() {
    return await callWebApi(this.getHttpsServer('channels/owned'));
  }
  async getChannelSubscribes() {
    return await callWebApi(this.getHttpsServer('channels/subscribes'));
  }
  async getChannel(channelId: number) {
    return await callWebApi(this.getHttpsServer(`channels/${channelId}`));
  }
  async putChannelSubscribe(channelId: number) {
    return await this.setUserData(`subscribe,${channelId}`, { channelId });
  }
  async putChannelUnsubscribe(channelId: number) {
    return await this.setUserData(`unsubscribe,${channelId}`, { channelId });
  }
  async getProgramsRecommend(nextToken: string | null, search?: string) {
    let params = '';
    if (nextToken) {
      params += `?nextToken=${nextToken}`;
    }
    if (search) {
      params += params ? `&search=${search}` : `?search=${search}`;
    }
    return await callWebApi(this.getHttpsServer(`channels/programs/recommend${params}`));
  }
  async getPrograms(channelId: number, tag?: string) {
    let params = `?channelId=${channelId}`;
    if (tag) {
      params += `&tag=${tag}`;
    }
    return await callWebApi(this.getHttpsServer(`channels/programs${params}`));
  }
  async getUserProgramsByType(type: string, nextToken: string | null, search?: string) {
    let params = '';
    if (nextToken) {
      params += `?nextToken=${nextToken}`;
    }
    if (search) {
      params += params ? `&search=${search}` : `?search=${search}`;
    }
    return await callWebApi(this.getHttpsServer(`channels/programs/user/${type}${params}`));
  }
  async getProgram(id: number) {
    return await callWebApi(this.getHttpsServer(`channels/programs/${id}`));
  }
  async postProgramLike(id: number) {
    return await callWebApi(this.getHttpsServer(`channels/programs/${id}/like`), 'POST');
  }
  async postProgramUnlike(id: number) {
    return await callWebApi(this.getHttpsServer(`channels/programs/${id}/unlike`), 'POST');
  }
  async postProgramCollect(id: number) {
    return await callWebApi(this.getHttpsServer(`channels/programs/${id}/collect`), 'POST');
  }
  async postProgramUncollect(id: number) {
    return await callWebApi(this.getHttpsServer(`channels/programs/${id}/uncollect`), 'POST');
  }
  async putProgramShare(id: number) {
    return await callWebApi(this.getHttpsServer(`channels/programs/${id}/share`), 'PUT');
  }
  async putProgramViews(id: number) {
    return await callWebApi(this.getHttpsServer(`channels/programs/${id}/views`), 'PUT');
  }
  async getBibleData() {
    return await callWebApi(this.getHttpsServer('bible/data'));
  }
  async getBibleChapters(bookId: number, translation?: ETranslation) {
    return await callWebApi(
      this.getHttpsServer(`bible/${bookId}/chapters${translation ? `?translation=${translation}` : ''}`)
    );
  }

  async getBibleSearch(queryParams: string) {
    return await callWebApi(this.getHttpsServer(`bible/search?${queryParams}`));
  }
}

export { iDigestDS };

import * as Clipboard from 'expo-clipboard';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import * as WebBrowser from 'expo-web-browser';

import {
  ActivityIndicator,
  Dimensions,
  Keyboard,
  Platform,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View
} from 'react-native';
import { AvatarIcon, LoadingIndicator, ModalPopup, getGroupIcon, getImage } from '@/components';
import { BorderlessButton, Pressable, RectButton } from 'react-native-gesture-handler';
import { FM_ROUNDED_CORNER_RADIUS, FM_SCREEN_PADDING_HORIZONTAL } from '@/fishMeet/styles/fmStyles';
import { FmPlusIcon, FmSendIcon } from '@/fishMeet/components/FmIcons';
import { GiftedChat, MessageText } from 'react-native-gifted-chat';
import React, { useContext, useState } from 'react';
import { convertFileSizeToString, openUrl } from '@/utils/helper';
import { getCurrentUser, getHttpsServer } from '@/utils/user';
import { getObjectAsync, setObjectAsync } from '@/dataStorage/localStorage';
import { notifyPermissionIssue, pickFileAsync, pickImageAsync, takePictureAsync } from '@/utils/media';

import { AppContext } from '@/context/AppContext';
import { Audio } from 'expo-av';
import { Avatar2 } from '@/components/Avatar2';
import { Colors } from '@/styles/colors';
import { FmColors } from '@/fishMeet/styles/fmColors';
import { FmText } from '@/fishMeet/components/FmText';
import { Image } from 'expo-image';
import ImagePreview from '@/components/ImagePreview';
import { InputField } from '@/fishMeet/components/InputField';
import KeyboardSpacer from '@/components/KeyboardSpacer';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { Overlay } from '@rneui/themed';
import ParsedText from 'react-native-parsed-text';
import RNURLPreview from '@/components/UrlPreview';
import SearchBar from '@/components/SearchBar';
import Sound from '@/utils/sound';
import { cloneDeep } from 'lodash-es';
import { getChatUIStyles } from '@/fishMeet/styles/screens/chatUIStyle';
import { getFontSize } from '@/utils/getFontSize';
import { i18n2 } from '@/utils/i18n2';
import { isFishMeet } from '@/utils/deviceOrAppType';
import { launchMeetingAsync } from '@/utils/meeting';
import { showMessage } from '@/components/MessageBox';

const imagePrefix = '!image!';
const audioPrefix = '!audio!';
const filePrefix = '!file!';
const contactPrefix = '!contact!';

const parsedTextPattern = /[\w.:_-]+:\/\/[\w./_=#@,~!$'*+;&()%:?-]+/i;

const shadowEffect = {
  shadowOffset: { width: 0.15, height: 0.15 },
  shadowColor: '#AAAAAA',
  shadowOpacity: 0.25,
  marginTop: 5,
  padding: 3,
  borderRadius: isFishMeet ? FM_ROUNDED_CORNER_RADIUS : 10
};

const leftStyles = {
  ...shadowEffect,
  borderColor: '#C1C1C1',
  borderWidth: 1,
  backgroundColor: isFishMeet ? FmColors.lightBackground : '#FFF'
};

const rightStyles = {
  ...shadowEffect,
  backgroundColor: isFishMeet ? FmColors.darkBackground : '#EBEBEB'
};

function isSameDayMessage(msg1, msg2) {
  if (!msg1 || !msg2) {
    return false;
  }

  const d1 = new Date(msg1.createdAt);
  const d2 = new Date(msg2.createdAt);
  if (!d1 || !d2) {
    return false;
  }

  return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
}

const resolveUriToPath = async (uri, fileName) => {
  let fileUri = uri;
  let fileSize = 0;
  try {
    //The original path to access Android is restricted, and you need to copy an RN accessible path.
    if (Platform.OS === 'android') {
      const destinationUri = `${FileSystem.documentDirectory}${fileName}`;
      await FileSystem.copyAsync({
        from: uri,
        to: destinationUri
      });
      fileUri = destinationUri;
    }
    const fileInfo = await FileSystem.getInfoAsync(fileUri);
    if (fileInfo.exists) {
      fileSize = fileInfo.size;
    }
  } catch (error) {
    console.error('Error resolving content URI:', error);
  }
  return { fileUri, fileSize };
};

export default class ChatUI extends React.Component {
  static contextType = AppContext;

  constructor(props) {
    super(props);
    this.listeners = [];
    this.chatId = `${props.chatId}`;
    this.group = props.group;
    this.enableAudioChat = !!props.enableAudioChat;
    this.isGroupLeader = this.group.isGroupLeader;
    this.groupId = this.group.groupId;
    this.userId = props.userId;
    this.isAnonymous = !!props.isAnonymous;
    this.chatServer = props.chatServer;
    this.enableTaggingLink = !!props.enableTaggingLink;
    this.showInput = !!props.showInput;
    this.showGroups = props.showGroups || [];
    this.shareInGroups = this.showGroups.length > 0 ? JSON.stringify([0]) : null;
    this.shareIntentData = props.shareIntentData;

    this.recording = null;
    this.sound = new Sound();
    this.playingMessageId = null;
    this.shouldPlayAtEndOfSeek = false;
    this.recordingSettings = {
      android: {
        extension: '.m4a',
        outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
        audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
        sampleRate: 44100,
        numberOfChannels: 2,
        bitRate: 64000,
        linearPCMBitDepth: 16,
        linearPCMIsBigEndian: false,
        linearPCMIsFloat: false
      },
      ios: {
        extension: '.wav',
        audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_MEDIUM,
        sampleRate: 44100,
        numberOfChannels: 1,
        bitRate: 64000,
        linearPCMBitDepth: 16,
        linearPCMIsBigEndian: false,
        linearPCMIsFloat: false
      }
    };
    this.localLastMessage = -1;
    this.inputToolbarHeight = 44;
    this.keyboardListeners = [];
    this.messageFromTime = Number.MAX_VALUE;
    this.lastLoadEarlierMessagesTime = -1;
    this.allowContinuePlaying = false;
    this.useNewFav = !this.group.isMomentDiscussionGroup && !this.group.isClassDiscussionGroup;
    this.enableTagging = !!props.enableTagging || this.useNewFav;
    this.cursorPos = 0;

    this.state = {
      busy: false,
      showAccessory: false,
      showBottomSheet: false,
      messages: props.messages || [],
      message: getCurrentUser().getProperty(`chat.draft.${this.chatId}`) || '',
      soundPosition: null,
      soundDuration: null,
      recordingDuration: null,
      shouldPlay: false,
      isPlaying: false,
      isLoadingSound: false,
      isRecording: false,
      isTextMode: true,
      textInputHeight: 20,
      keyboardShown: false,
      replyMessageId: getCurrentUser().getProperty(`chat.draft.replyId.${this.chatId}`) || -1,
      replyMessageText: getCurrentUser().getProperty(`chat.draft.replyText.${this.chatId}`) || '',
      currentTag: props.currentTag,
      showQuoteMessage: '',
      showModal: false,
      tags: [],
      showSearchBox: false,
      searchText: '',
      currentImageIndex: -1
    };

    this.messagesLoaded = false;
    this.resetMessages = false;
    getObjectAsync(`chat/${this.chatId}`).then((result) => {
      if (this.messagesLoaded) {
        // in case message is loaded from server before cache is ready, we skip cache
        return;
      }

      this.setState({ ...result });

      // after this we will reset messages when it's loaded from server
      this.resetMessages = true;
    });

    // this.accessoryY = new Animated.Value(0);
    this.accessoryY = 0;
    // this.fall = new Animated.Value(1);
    this.fall = 1;
    this.longPressSelectedMessage = null;
    this.mentionedUsers = getCurrentUser().getProperty(`chat.draft.mentionedUsers.${this.chatId}`) || [];
    this.latestMessageId = 0;
    this.unmount = false;
    this.perGroupHideMessages = -1;

    this.chatServer.newMessageCallback = this.onNewMessage;
    this.chatServer.deleteMessageCallback = this.onDeleteMessage;
    this.chatServer.newGroupInfoCallback = this.onNewGroupInfo;

    if (!this.useNewFav) {
      // Removed existing tags
      this.chatServer.groupTagsCallback = this.onGroupTags;
    }
  }

  async componentDidMount() {
    // this.keyboardListeners.push(
    //   Keyboard.addListener('keyboardWillShow', () => {
    //     this.setState({ keyboardShown: true });
    //   })
    // );

    // this.keyboardListeners.push(
    //   Keyboard.addListener('keyboardWillHide', () => {
    //     this.setState({ keyboardShown: false });
    //   })
    // );

    this.focusListener = this.props.navigation.addListener('focus', () => {
      this.context.loadTagsAsync();
    });

    this.listeners.push(
      globalThis.eventEmitter.addListener('appInForeground', () => {
        // after app in foreground again, check new messages
        const { currentTag } = this.state;
        const data = {
          from: Number.MAX_SAFE_INTEGER.toString(),
          to: this.latestMessageId
        };
        if (this.shareInGroups) {
          data.shareInGroups = this.shareInGroups;
        } else {
          data.tag = currentTag;
        }
        this.chatServer.getMessagesAsync(data);
      })
    );

    this.listeners.push(
      globalThis.eventEmitter.addListener('appInBackground', () => {
        // after app in background, save the latest message id
        const { messages } = this.state;
        this.latestMessageId = messages.length > 0 ? messages[0]._id : 0;
      })
    );

    this.listeners.push(
      globalThis.eventEmitter.addListener('currentGroupUpdated', (data) => {
        if (`${data.groupId}` !== this.chatId) {
          return;
        }
        if (typeof data.hideMessages === 'number' && data.hideMessages > 0) {
          this.perGroupHideMessages = data.hideMessages;
          console.log(`currentGroupUpdated - set perGroupHideMessages=${this.perGroupHideMessages}`);
        }
      })
    );

    this.soundListener = this.sound.addListener(this.updateScreenForSoundStatus);
    await this.sound.enableAsync();
    this.onDisappearingMessages();
    if (this.shareIntentData) {
      setTimeout(async () => {
        if (typeof this.shareIntentData === 'string') {
          this.setState({ message: this.shareIntentData });
          this.sendMessageAsync();
        } else {
          const fileCategories = {
            image: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'],
            video: ['.mp4', '.avi', '.mkv', '.mov', '.wmv'],
            audio: ['.mp3', '.wav', '.ogg', '.flac', '.m4a', '.aac']
          };
          const extension = `.${this.shareIntentData.fileName.split('.').pop().toLowerCase()}`;
          const { fileUri, fileSize } = await resolveUriToPath(this.shareIntentData.uri, this.shareIntentData.fileName);
          const createMessage = (category) => ({
            [category]: {
              uri: fileUri,
              fileName: this.shareIntentData.fileName,
              fileSize: fileSize
            }
          });
          let message;
          if (Object.keys(fileCategories).find((category) => fileCategories[category].includes(extension))) {
            message = createMessage(
              Object.keys(fileCategories).find((category) => fileCategories[category].includes(extension))
            );
          } else {
            message = {
              file: {
                uri: fileUri,
                name: this.shareIntentData.fileName,
                size: fileSize
              }
            };
          }
          this.sendAttachmentAsync(message);
        }
      }, 1000);
    }
  }

  componentWillUnmount() {
    this.unmount = true;
    this.onActionsStopRecordingAsync(false);
    this.soundListener?.remove();
    this.sound.resetAsync();
    this.keyboardListeners.forEach((listener) => {
      listener.remove();
    });
    this.listeners.forEach((listener) => {
      listener.remove();
    });
  }

  reloadMessages = () => {
    this.setState({ showSearchBox: false, messages: [], currentTag: undefined });
    this.chatServer.getMessagesAsync({});
  };

  getMessageTime(message) {
    if (!message || !message.createdAt) {
      console.log('Invalid message!', message);
      return -1;
    }

    return new Date(message.createdAt).getTime();
  }

  onDeleteMessage = (message) => {
    this.setState((previousState) => {
      return { messages: previousState.messages.filter((msg) => msg._id !== message.messageId) };
    });
  };

  onDisappearingMessages = () => {
    if (this.unmount) {
      return;
    }

    setTimeout(() => {
      this.onDisappearingMessages();
    }, 1000);

    const { messages } = this.state;
    for (const message of messages) {
      if (!this.isMessageValid(message)) {
        console.log('Clean up invalid messages!');
        this.setState((previousState) => ({
          messages: previousState.messages.filter(this.isMessageValid)
        }));
      }
    }
  };

  isMessageValid = (message) => {
    // in tag mode, always return true
    if (this.state.currentTag) {
      return true;
    }

    // handles 3 types of disappearing messages:
    // 1. auto-delete message - set by message.deleteAt
    if (message.deleteAt > 0 && message.deleteAt * 1000 <= Date.now()) {
      // message is expired
      console.log('message is expired', message);
      return false;
    }

    // 2. auto-hide message - set by user globally
    const { hideMessages } = this.context.userProfile;
    if (message.time && typeof hideMessages === 'number' && hideMessages > 0) {
      // message is prior to user hideMessages
      const deleteAt = parseInt(Date.now() / 1000) - hideMessages * 60;
      if (message.time <= deleteAt) {
        console.log('message is prior to user hideMessages', message);
        return false;
      }
    }

    // 3. auto-hide message - set by user per-group
    if (this.perGroupHideMessages > 0) {
      // message is prior to user's perGroupHideMessages
      const deleteAt = parseInt(Date.now() / 1000) - this.perGroupHideMessages * 60;
      if (message.time <= deleteAt) {
        console.log('message is prior to group hideMessages', message);
        return false;
      }
    }

    return true;
  };

  onGroupTags = (tags) => {
    if (this.showGroups.length > 0) {
      // for discussion group, return ShareInGroup ids
      // if user post answers to a specific group (currentTag), we will make it first (after 0 - public)
      const newTags = this.props.currentTag !== undefined ? [this.props.currentTag] : [];
      // some share in group may not be associated with the class, we will skip them for now
      tags.forEach((tag) => {
        const tagValue = parseInt(tag);
        if (
          newTags.indexOf(tagValue) === -1 &&
          (tagValue === 0 ||
            (tagValue !== this.props.currentTag && this.showGroups.findIndex((it) => it.groupId === tagValue) !== -1))
        ) {
          newTags.push(tagValue);
        }
      });
      // it's possible that this.props.currentTag doesn't exist in newTags, so we will reset it
      if (newTags.length > 0 && newTags.indexOf(this.props.currentTag) === -1) {
        this.setState({ currentTag: newTags[0], tags: newTags, messages: [] });
        this.shareInGroups = JSON.stringify([newTags[0]]);
        this.chatServer.getMessagesAsync({ shareInGroups: this.shareInGroups });
      } else {
        this.setState({ tags: newTags });
      }

      setObjectAsync(`chat/${this.chatId}`, { tags: newTags });
    } else {
      // for chat group, return tag names
      const pos = tags.indexOf('PrayerAnswered');
      if (pos !== -1) {
        tags.splice(pos, 1);
      }

      // always add these tags
      const alwaysShowTags = this.group.isOrgGroup
        ? ['OnlineInfo', 'NeedPrayer', 'OrgFavorite']
        : ['OnlineInfo', 'NeedPrayer', 'GroupFavorite'];
      alwaysShowTags.reverse().forEach((tag) => {
        if (tags.indexOf(tag) === -1) {
          tags.unshift(tag);
        }
      });

      this.setState({ tags });
      setObjectAsync(`chat/${this.chatId}`, { tags });
    }
  };

  onNewGroupInfo = (info) => {
    if (typeof this.props.onChatInfoUpdated === 'function') {
      this.props.onChatInfoUpdated(info);
    }
  };

  onNewMessage = (newMessages) => {
    if (!Array.isArray(newMessages)) {
      return;
    }
    // filter invalid messages
    newMessages = newMessages.filter((it) => this.isMessageValid(it));

    // set message to loaded
    this.messagesLoaded = true;

    let messages = [];
    if (this.resetMessages) {
      // reset messages from cache (start from empty), use the one loaded from server
      this.resetMessages = false;

      // reset message cache & state
      this.setState({ messages: [] });
      setObjectAsync(`chat/${this.chatId}`, { messages: [] });
    } else {
      messages = this.state.messages;
    }

    // filter out duplicate & disappearing messages
    newMessages = newMessages
      .filter((n) => messages.findIndex((o) => o._id === n._id) === -1)
      .filter((item) => !item.deleteAt || item.deleteAt * 1000 > Date.now());
    if (newMessages.length === 0) {
      return;
    }

    const existingMessages = messages;
    const existingMessageLength = existingMessages.length;

    const timestampFromReceivedMessages = this.getMessageTime(newMessages[0]);
    const timestampFromExistingMessages = existingMessageLength > 0 ? this.getMessageTime(existingMessages[0]) : -1;

    const startTimeFromReceivedMessages = this.getMessageTime(newMessages[newMessages.length - 1]);
    const startTimeFromExistingMessages =
      existingMessageLength > 0 ? this.getMessageTime(existingMessages[existingMessageLength - 1]) : -1;
    if (startTimeFromExistingMessages === -1) {
      this.messageFromTime = startTimeFromReceivedMessages;
    } else {
      this.messageFromTime = Math.min(startTimeFromExistingMessages, startTimeFromReceivedMessages);
    }

    // prepend when received message is older, otherwise append newer message
    const prepend = timestampFromExistingMessages > timestampFromReceivedMessages;
    const combinedMessages = prepend
      ? GiftedChat.prepend(existingMessages, newMessages)
      : GiftedChat.append(existingMessages, newMessages);
    this.setState({ messages: combinedMessages });
    setObjectAsync(`chat/${this.chatId}`, { messages: combinedMessages });
  };

  isNearTop({ layoutMeasurement, contentOffset, contentSize }) {
    const topPadding = 80;

    // content height and layout measurement values are float. The comparison can return unexpected result if they have different precision level.
    // Convert them to int to ensure the same precision level to compare.
    let contentHeight = Math.round(contentSize.height);
    const layoutHeight = Math.round(layoutMeasurement.height);

    if (contentHeight <= layoutHeight) {
      return false;
    }

    // on Android, with Expo SDK 50 and RN 0.73, the content height is about one page of layout more than reality.
    // This should be a bug. To workaround this, we deduct it here to make the infinite scroll work.
    // This can also be a perf improvement for client scrolling experience after the Expo/RN fix
    // because we'll pre-fetch the data one screen before we hit the top of the current content size.
    if (Platform.OS === 'android') {
      contentHeight -= layoutHeight;
    }
    const result = contentHeight - layoutHeight - topPadding <= Math.round(contentOffset.y);
    return result;
  }

  loadEarlierMessagesAsync = async () => {
    if (this.lastLoadEarlierMessagesTime === this.messageFromTime) {
      console.log(`Already called getMessages for ${this.messageFromTime}, skipping`);
      return;
    }

    this.lastLoadEarlierMessagesTime = this.messageFromTime;

    const { messages, currentTag, searchText } = this.state;
    if (messages.length === 0) {
      return;
    }

    const fromId = messages[messages.length - 1].messageId;
    const data = { from: fromId, search: searchText };
    if (this.shareInGroups) {
      data.shareInGroups = this.shareInGroups;
    } else {
      data.tag = currentTag;
    }
    await this.chatServer.getMessagesAsync(data);
  };

  onTagPress = (tag) => {
    if (this.state.currentTag !== tag) {
      this.setState({ currentTag: tag, messages: [] });
      this.chatServer.getMessagesAsync({ tag });
    }
  };

  onLongPress = (message) => {
    this.longPressSelectedMessage = message;
    this.setState({ showModal: true });
  };

  updateScreenForSoundStatus = (status) => {
    console.log(`${status.isPlaying}..${status.isLoaded}..${status.positionMillis}..${status.durationMillis}`);
    if (!status.isPlaying && status.durationMillis === status.positionMillis) {
      if (this.allowContinuePlaying && status.positionMillis !== undefined) {
        const { messages } = this.state;
        const index = messages.findIndex((it) => it._id === this.playingMessageId);
        if (index > 0) {
          for (let i = index - 1; i >= 0; i--) {
            if (messages[i].message.startsWith(audioPrefix)) {
              this.playBubbleAudioAsync(messages[i]);
              break;
            }
          }
        }
      }

      this.playingMessageId = null;
    }
    if (status.isLoaded) {
      this.setState({
        soundDuration: status.durationMillis,
        soundPosition: status.positionMillis,
        shouldPlay: status.shouldPlay,
        isPlaying: status.isPlaying,
        isLoadingSound: !status.isPlaying && this.state.isLoadingSound
      });
    } else {
      this.setState({
        soundDuration: null,
        soundPosition: null
      });
      if (status.error) {
        console.log(`FATAL PLAYER ERROR: ${status.error}`);
      }
    }
  };

  updateScreenForRecordingStatus = (status) => {
    this.setState({ isRecording: status.isRecording });
    if (status.isRecording) {
      this.setState({ recordingDuration: status.durationMillis });
    }

    console.log(status);
  };

  async onActionsStartRecordingAsync() {
    if (this.state.busy) {
      return;
    }

    if (this.recording) {
      return;
    }

    try {
      const { granted } = await Audio.requestPermissionsAsync();
      if (!granted) {
        notifyPermissionIssue('Audio');
        return;
      }
    } catch (error) {
      alert(error);
      return;
    }

    try {
      await Audio.setAudioModeAsync({ allowsRecordingIOS: true });

      const recording = new Audio.Recording();
      await recording.prepareToRecordAsync(this.recordingSettings);
      recording.setOnRecordingStatusUpdate(this.updateScreenForRecordingStatus);

      this.recording = recording;
      await this.recording.startAsync(); // Will call this.updateScreenForRecordingStatus to update the screen.

      console.log('Recording started');
    } catch (error) {
      alert(error);
      this.recording = null;
    }
  }

  async onActionsStopRecordingAsync(upload) {
    if (this.state.busy) {
      return;
    }

    if (!this.recording) {
      return;
    }

    try {
      await this.recording.stopAndUnloadAsync();

      await Audio.setAudioModeAsync({ allowsRecordingIOS: false });

      console.log({ audio: this.recording.getURI(), duration: this.state.recordingDuration });

      // only send audio message > 1s
      if (upload && this.state.recordingDuration > 1000) {
        this.sendAttachmentAsync({ audio: { uri: this.recording.getURI(), duration: this.state.recordingDuration } });
      }
    } catch (error) {
      // Do nothing -- we are already unloaded.
      console.log(`onActionsStopRecordingAsync() error : ${error}`);
    } finally {
      this.recording = null;
    }

    console.log('Recording stopped');
  }

  sendAttachmentAsync = async (message) => {
    try {
      this.setState({ busy: true });

      let localUri;
      const info = {
        groupId: this.groupId,
        type: null,
        duration: null,
        fileSize: null,
        displayName: null,
        isAnonymous: this.isAnonymous,
        shareInGroup: this.showGroups.length > 0 ? this.state.currentTag : 0
      };
      if (message.image) {
        let actions = [];
        const MaxImageWidth = 1080;
        if (message.image.width > MaxImageWidth) {
          // Resize image if width is exceeding MaxImageWidth
          const ratio = message.image.width / MaxImageWidth;
          actions.push({ resize: { width: MaxImageWidth, height: message.image.height / ratio } });
        }

        // Compress image to 75% JPEG
        const result = await ImageManipulator.manipulateAsync(message.image.uri, actions, {
          compress: 0.75,
          format: ImageManipulator.SaveFormat.JPEG
        });

        localUri = result.uri;

        info.type = 'image';
        info.fileSize = message.image.fileSize;
        info.displayName = message.image.fileName;
      } else if (message.video) {
        localUri = message.video.uri;
        // File:  !file!filename!fileSize!
        if (Platform.OS === 'ios') {
          // fileName + fileSize are iOS only
          info.fileSize = message.video.fileSize;
          info.displayName = message.video.fileName;
        } else {
          // we will check file in Android
          const fileInfo = await FileSystem.getInfoAsync(localUri);
          info.fileSize = fileInfo.size;
          info.displayName = i18n2.t('VideoFile');
        }
        info.type = 'video';
        info.duration = message.video.duration;
      } else if (message.audio) {
        localUri = message.audio.uri;
        info.type = 'audio';
        info.duration = message.audio.duration;
      } else if (message.file) {
        localUri = message.file.uri;
        info.type = 'file';
        info.displayName = message.file.name;
        info.fileSize = message.file.size;
      } else {
        alert(i18n2.t('Error'));
        return;
      }

      let queryParam = '';
      for (const key in info) {
        if (info[key] !== null) {
          queryParam += `&${key}=${encodeURIComponent(info[key])}`;
        }
      }
      const succeed = await globalThis.dsObject.sendChatFileMessage(localUri, queryParam.substring(1));
      if (!succeed) {
        return;
      }

      this.setState({ replyMessageId: -1, replyMessageText: '' });
    } catch (error) {
      alert(error);
    } finally {
      this.setState({ busy: false });
    }
  };

  async playBubbleAudioAsync(message) {
    this.allowContinuePlaying = true;
    const items = message.text.split('!');
    const url = this.getFileUrl(items[3]);
    try {
      if (this.playingMessageId !== message._id) {
        await this.sound.resetAsync();
        console.log(`play new : ${message._id} ${url}`);
        this.setState({
          isLoadingSound: true
        });
        await this.sound.loadAsync(url);
        await this.sound.playAsync();
        this.playingMessageId = message._id;
      } else {
        if (this.state.isPlaying) {
          console.log(`pause old : ${message._id}`);
          await this.sound.pauseAsync();
        } else {
          console.log(`resume old : ${message._id}`);
          await this.sound.playAsync();
        }
      }
    } catch (error) {
      console.log(error);
      this.setState({
        isLoadingSound: false
      });
    }
  }

  async previewUploadedFileAsync(fileUrl, fileName) {
    try {
      let url = this.getFileUrl(fileUrl);
      if (fileName) {
        url += '/' + encodeURIComponent(fileName);
      }
      console.log(url);

      openUrl(url, this.props.navigation, this.context);
    } catch (error) {
      console.log(error);
    }
  }

  getFileUrl(url) {
    url = url || '';
    if (!url.toLowerCase().startsWith('http')) {
      url = getHttpsServer(`chat/file/${url}`);
    }

    return url;
  }

  shareToGroupAsync = async (isAnonymous) => {
    const result = await globalThis.dsObject.getGroups();
    if (!result) {
      return;
    }

    const choices = result.body.filter(
      (studyGroup) =>
        studyGroup.status &&
        studyGroup.groupId !== this.groupId &&
        (!studyGroup.isOneOnOneGroup ||
          (studyGroup.isOneOnOneGroup &&
            studyGroup.friendUserId &&
            !this.context.blockedBy.includes(studyGroup.friendUserId) &&
            !this.context.blocks[studyGroup.friendUserId]))
    );

    this.props.navigation.navigate('Select', {
      choices,
      title: isAnonymous ? i18n2.t('ShareToGroupAnon') : i18n2.t('ShareToGroup'),
      getDisplayName: (choice) => choice.name,
      onRenderItem: (choice) => {
        return (
          <View
            style={{
              marginTop: 10,
              marginHorizontal: 10,
              backgroundColor: Colors.lightBlue,
              borderColor: Colors.darkBlue,
              borderWidth: 1,
              borderRadius: 25,
              height: 50,
              paddingHorizontal: 10,
              alignItems: 'baseline',
              justifyContent: 'center'
            }}>
            <View style={{ flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
              {getGroupIcon(choice)}
              <Text
                style={{
                  fontSize: getCurrentUser().getSmallFontSize(),
                  fontWeight: 'bold',
                  paddingLeft: 7,
                  color: Colors.darkBlue
                }}
                numberOfLines={1}>
                {choice.name}
              </Text>
            </View>
          </View>
        );
      },
      onSelect: async (choice) => {
        const disablePostMessage = !this.isGroupLeader && !!this.group.onlyLeaderCanPost;
        if (disablePostMessage) {
          return;
        }
        const result = await this.chatServer.sendMessageAsync({
          room: choice.groupId,
          message: this.longPressSelectedMessage.text,
          isAnonymous
        });
        if (result) {
          showMessage({
            message: i18n2.t('MsgSent')
          });
        } else {
          showMessage({
            message: i18n2.t('Errors.ErrMsgSend'),
            type: 'danger'
          });
        }
      }
    });
  };

  renderImageBubble(props) {
    const items = props.currentMessage.text.split('!');
    let imageUri = items[2] || '';
    let previewImageUri;
    const BubbleComponent = isFishMeet ? FmBubble : IDigestBubble;
    // TODO: [Wei] implement it in nodejs
    if (imageUri.toLowerCase().startsWith('http')) {
      previewImageUri = getHttpsServer(`img/?url=${imageUri}`);
    } else {
      imageUri = getHttpsServer(`chat/file/${imageUri}`);
      previewImageUri = getHttpsServer(`img/?url=${imageUri}`);
    }
    return (
      <BubbleComponent
        {...props}
        onPress={() => {
          if (Platform.OS === 'ios') {
            const index = this.state.messages
              .filter((item) => item.text && item.text.startsWith(imagePrefix))
              .reverse()
              .findIndex((item) => item._id === props.currentMessage._id);
            this.setState({ currentImageIndex: index });
          } else {
            WebBrowser.openBrowserAsync(imageUri);
          }
        }}>
        <Image
          style={{ width: Dimensions.get('window').width / 3, height: Dimensions.get('window').width / 4, margin: 7 }}
          contentFit='contain'
          source={{ uri: previewImageUri }}
        />
      </BubbleComponent>
    );
  }

  renderContactBubble(props) {
    const items = props.currentMessage.text.split('!');
    let contactUniqueId = items[2] || '';
    let contactDisplayName = items[3] || '';
    const fontSize = getCurrentUser().getMediumFontSize();
    const sFontSize = getCurrentUser().getSmallFontSize();
    const BubbleComponent = isFishMeet ? FmBubble : IDigestBubble;
    const TextComponent = isFishMeet ? FmText : Text;
    const contactNameTextStyle = isFishMeet
      ? getChatUIStyles().contactNameText
      : {
          marginLeft: 7,
          fontSize,
          textAlignVertical: 'center'
        };
    const contactCardTextStyle = isFishMeet
      ? getChatUIStyles().contactCardText
      : {
          width: 64,
          fontSize: sFontSize,
          textAlign: 'center',
          color: '#707070'
        };

    return (
      <BubbleComponent
        {...props}
        onPress={() => {
          this.props.navigation.navigate('AcceptContactCard', {
            name: contactDisplayName,
            uniqueId: contactUniqueId
          });
        }}>
        <View style={{ padding: 14, paddingBottom: 0 }}>
          <View style={{ flexDirection: 'row', padding: 0 }}>
            {getImage('defaultAvatar', { alignSelf: 'left', width: 60, height: 60 })}
            <TextComponent style={contactNameTextStyle}>{contactDisplayName}</TextComponent>
          </View>
          <TextComponent style={contactCardTextStyle}>{i18n2.t('ContactCard')}</TextComponent>
        </View>
      </BubbleComponent>
    );
  }

  renderAudioBubble(props) {
    // !audio!duration!url
    const items = props.currentMessage.text.split('!');
    const duration = parseInt(items[2]);
    const text = isNaN(duration) ? i18n2.t('InvalidAudio') : `${(duration / 1000).toFixed(1)}"`;
    const BubbleComponent = isFishMeet ? FmBubble : IDigestBubble;
    const TextComponent = isFishMeet ? FmText : Text;
    const textStyle = isFishMeet
      ? getChatUIStyles().audioText
      : {
          color: '#202020',
          fontSize: getCurrentUser().getSmallFontSize()
        };
    return (
      <BubbleComponent
        {...props}
        onPress={() => {
          this.playBubbleAudioAsync(props.currentMessage);
        }}>
        <View
          style={{
            padding: 5,
            alignItems: 'center',
            flexDirection: 'row'
          }}>
          <TextComponent style={textStyle}>{text}</TextComponent>
          {getImage('voice')}
        </View>
      </BubbleComponent>
    );
  }

  renderFileBubble(props) {
    const items = props.currentMessage.text.split('!');
    const fileName = decodeURIComponent(items[2] || '');
    const fileSize = parseInt(items[3]) || 0;
    const fileUrl = items[items.length - 1];
    const text = `${fileName}${convertFileSizeToString(fileSize)}`;
    const BubbleComponent = isFishMeet ? FmBubble : IDigestBubble;
    const TextComponent = isFishMeet ? FmText : Text;
    const textStyle = isFishMeet
      ? getChatUIStyles().fileText
      : {
          color: '#202020',
          textDecorationLine: 'underline',
          fontSize: getCurrentUser().getSmallFontSize(),
          paddingHorizontal: 7
        };
    return (
      <BubbleComponent
        {...props}
        onPress={() => {
          this.previewUploadedFileAsync(fileUrl, fileName);
        }}>
        <TextComponent style={textStyle}>{text}</TextComponent>
      </BubbleComponent>
    );
  }

  renderTextBubble = (props) => {
    props.currentMessage.text = props.currentMessage.text.replace(/\\n/g, '\n');
    const BubbleComponent = isFishMeet ? FmBubble : IDigestBubble;
    return (
      <BubbleComponent
        {...props}
        text={props.currentMessage.text}
        onPress={(url) => {
          if (url) {
            openUrl(url, this.props.navigation, this.context);
          }
        }}
        onQuotePress={(message) => {
          this.setState({ showQuoteMessage: message });
        }}
      />
    );
  };

  addTagToSelectedMessageAsync = async (tag) => {
    const messageId = this.longPressSelectedMessage._id;
    const result = await globalThis.dsObject.setChatTagMessage(messageId, tag);
    if (!result) {
      return;
    }

    this.longPressSelectedMessage.tags = result.body.tags || '';
    // Force update gifted chat UI
    this.setState({ messages: cloneDeep(this.state.messages) });
  };

  removeTagFromSelectedMessageAsync = async (tag) => {
    const messageId = this.longPressSelectedMessage._id;
    const result = await globalThis.dsObject.deleteChatTagMessage(messageId, tag);
    if (!result) {
      return;
    }

    this.longPressSelectedMessage.tags = result.body.tags || '';
    // Force update gifted chat UI
    this.setState({ messages: cloneDeep(this.state.messages) });
  };

  addCollectionAsync = async (target) => {
    // groupId, target, attachment, message, user, time
    const {
      user: { _id },
      attachment,
      text,
      time
    } = this.longPressSelectedMessage;
    const body = {
      attachment,
      message: text,
      user: _id,
      time
    };
    const result = await globalThis.dsObject.setCollection(this.chatId, target, body);
    if (result) {
      showMessage({
        type: 'success',
        message: i18n2.t('OpsSuccess')
      });
    }
  };

  removeCollectionAsync = async (source) => {
    // groupId, target, attachment, message, user, time
    const { messageId } = this.longPressSelectedMessage;
    const result = await globalThis.dsObject.deleteCollection(this.chatId, source, messageId);
    if (!result) {
      return;
    }

    this.onDeleteMessage(this.longPressSelectedMessage);
    this.longPressSelectedMessage = null;

    showMessage({
      type: 'success',
      message: i18n2.t('OpsSuccess')
    });
  };

  addTag = (items, tags, tag) => {
    const hasTag = tags.indexOf(tag) !== -1;
    tag = tag.substr(1);
    items.push({
      hasTag,
      text: `[${i18n2.t(tag === 'NeedPrayer' ? 'Prayer' : tag)}]`,
      onPress: async () => {
        if (hasTag) {
          await this.removeTagFromSelectedMessageAsync(tag);
        } else {
          await this.addTagToSelectedMessageAsync(tag);
        }
      }
    });
  };

  renderPlay() {
    if (!this.state.isPlaying && !this.state.isLoadingSound) {
      return null;
    }
    return (
      <View
        style={{
          position: 'absolute',
          width: Dimensions.get('window').width,
          height: Dimensions.get('window').height
        }}>
        <TouchableOpacity
          activeOpacity={1}
          style={{
            width: Dimensions.get('window').width,
            height: Dimensions.get('window').height,
            backgroundColor: '#ffffffbb',
            alignItems: 'center'
          }}
          onPress={() => {
            if (this.state.isLoadingSound) {
              this.setState({
                isLoadingSound: false
              });
            }
            this.allowContinuePlaying = false;
            this.sound.resetAsync();
          }}>
          <View
            style={{
              top: 200,
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 20,
              backgroundColor: '#CDCDCD',
              ...shadowEffect,
              padding: 20
            }}>
            {getImage('playRecording')}
            <Text
              style={{
                fontSize: getCurrentUser().getXLargeFontSize(),
                minWidth: 120
              }}>
              {i18n2.t('Playing')} {(this.state.soundPosition / 1000).toFixed(1)}s
            </Text>
            {this.state.isLoadingSound && <ActivityIndicator />}
          </View>
        </TouchableOpacity>
      </View>
    );
  }

  renderRecording() {
    if (!this.state.isRecording) {
      return null;
    }

    return (
      <View
        style={{
          position: 'absolute',
          top: 200,
          width: Dimensions.get('window').width,
          alignItems: 'center'
        }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: 20,
            backgroundColor: '#CDCDCD',
            ...shadowEffect,
            padding: 20
          }}>
          {getImage('recording')}
          <Text
            style={{
              fontSize: getCurrentUser().getXLargeFontSize(),
              paddingHorizontal: 10
            }}>
            {i18n2.t('Recording')} {(this.state.recordingDuration / 1000).toFixed(1)}s
          </Text>
        </View>
      </View>
    );
  }

  renderFullScreenMessage() {
    if (!this.state.showQuoteMessage) {
      return null;
    }

    const fontSize = getCurrentUser().getXLargeFontSize();
    return (
      <Overlay
        isVisible={true}
        fullScreen={true}
        overlayStyle={{
          paddingTop: this.context.insets.top,
          paddingBottom: this.context.insets.bottom,
          paddingLeft: this.context.insets.left,
          paddingRight: this.context.insets.right
        }}
        supportedOrientations={['portrait', 'landscape']}
        onBackdropPress={() => {
          this.setState({ showQuoteMessage: '' });
        }}>
        <View style={{ flex: 1, position: 'absolute', top: 0, bottom: 0, zIndex: 8, backgroundColor: '#ffffff' }}>
          <ScrollView
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: 'center',
              alignItems: 'center',
              paddingHorizontal: 20,
              paddingVertical: this.context.insets.top
            }}>
            <ParsedText
              style={{
                padding: 5,
                color: '#202020',
                fontSize,
                lineHeight: fontSize * 1.8
              }}
              parse={[
                {
                  pattern: parsedTextPattern,
                  style: { textDecorationLine: 'underline', color: Colors.darkBlue },
                  onPress: (url) => {
                    openUrl(url, this.props.navigation, this.context);
                  },
                  onLongPress: () => {
                    if (this.props.onLongPress) {
                      this.props.onLongPress();
                    }
                  }
                }
              ]}
              onPress={() => {
                this.setState({ showQuoteMessage: '' });
              }}
              childrenProps={{ allowFontScaling: false }}>
              {this.state.showQuoteMessage}
            </ParsedText>
          </ScrollView>
        </View>
      </Overlay>
    );
  }

  renderLoadingIndicator() {
    if (!this.state.busy) {
      return null;
    }

    return <LoadingIndicator />;
  }

  sendMessageAsync = async () => {
    try {
      const { message, replyMessageId, currentTag } = this.state;
      const disablePostMessage = !this.isGroupLeader && !!this.group.onlyLeaderCanPost;
      if (!message || disablePostMessage) {
        return;
      }

      const mentionedUsers = [];
      for (const user of this.mentionedUsers) {
        if (message.indexOf(`@${user.name} `) !== -1 || message.indexOf(`@${i18n2.t('ChatScreen.AllUsers')}`) !== -1) {
          mentionedUsers.push(user.userId);
        }
      }

      this.setState({ busy: true });
      const result = await this.chatServer.sendMessageAsync({
        room: this.chatId,
        message,
        isAnonymous: this.isAnonymous,
        mentionedUsers,
        replyMessageId,
        shareInGroup: this.showGroups.length > 0 ? currentTag : 0
      });
      if (!result) {
        showMessage({
          message: i18n2.t('Errors.ErrMsgSend'),
          type: 'danger'
        });
        return;
      }

      // message sent, clear draft
      this.textInput?.clear();
      await getCurrentUser().resetPropertyAsync(`chat.draft.${this.chatId}`, '');
      await getCurrentUser().resetPropertyAsync(`chat.draft.replyId.${this.chatId}`, -1);
      await getCurrentUser().resetPropertyAsync(`chat.draft.replyText.${this.chatId}`, '');
      await getCurrentUser().resetPropertyAsync(`chat.draft.mentionedUsers.${this.chatId}`, []);

      this.setState({ message: '', replyMessageId: -1, replyMessageText: '' });
      this.mentionedUsers = [];
    } catch (error) {
      showMessage({
        message: i18n2.t('Errors.Error'),
        description: i18n2.t('Errors.MsgSendFailed') + '\n\n' + error,
        type: 'danger'
      });
    } finally {
      this.setState({ busy: false });
    }
  };

  showAccessory(bottomOffset) {
    const AccessoryHeight = 85;
    this.accessoryY = AccessoryHeight - bottomOffset;
    Keyboard.dismiss();
    this.setState({ showAccessory: true });
  }

  hideAccessory() {
    this.accessoryY = 0;
    this.setState({ showAccessory: false });
  }

  iDigestRenderInputBar = () => {
    const offset = Platform.select({
      ios: 24,
      android: 20
    });
    const showReply = this.state.isTextMode && this.state.replyMessageId !== -1;
    const inputBarHeight = Math.min(this.state.textInputHeight + offset, Dimensions.get('window').height / 3);
    const bottomOffset = this.context.insets.bottom;
    const screenWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    const replyHeight = this.state.isTextMode ? 30 : 0;
    // hide input bar
    const disablePostMessage = !this.isGroupLeader && !!this.group.onlyLeaderCanPost;
    return (
      <>
        <View
          style={{
            left: this.context.insets.left,
            width: screenWidth,
            height: inputBarHeight + (showReply ? replyHeight : 0),
            flexDirection: 'row',
            alignItems: 'flex-end',
            borderColor: Colors.lightBlue,
            borderTopWidth: 1,
            borderBottomWidth: 1,
            marginBottom: this.state.keyboardShown ? 0 : bottomOffset,
            zIndex: 1
          }}>
          {disablePostMessage && (
            <View
              style={{
                position: 'absolute',
                width: screenWidth,
                height: inputBarHeight + bottomOffset,
                bottom: -bottomOffset,
                zIndex: 99
              }}>
              <View
                style={{
                  justifyContent: 'center',
                  alignContent: 'center',
                  width: screenWidth,
                  height: inputBarHeight
                }}>
                <Text style={{ color: 'red', textAlign: 'center' }}>{i18n2.t('ChatScreen.DisableMembersChat')}</Text>
              </View>
            </View>
          )}
          <View
            style={{
              marginHorizontal: 7,
              bottom: 7,
              opacity: disablePostMessage ? 0.1 : 1
            }}>
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                if (this.state.showAccessory) {
                  this.hideAccessory();
                } else {
                  this.showAccessory(bottomOffset);
                }

                this.resetCurrentTagIfNeeded();
              }}>
              {getImage('plus')}
            </TouchableOpacity>
          </View>
          <View style={{ flex: 1, bottom: 4, opacity: disablePostMessage ? 0.1 : 1 }}>
            {!this.state.isTextMode ? (
              <TouchableOpacity
                activeOpacity={1}
                style={{
                  justifyContent: 'center',
                  alignItems: 'center',
                  backgroundColor: Colors.lightBlue,
                  borderWidth: 1,
                  borderRadius: 7,
                  borderColor: Colors.darkBlue,
                  paddingHorizontal: 9,
                  paddingVertical: 7,
                  fontSize: getCurrentUser().getSmallFontSize()
                }}
                onPressIn={() => {
                  this.onActionsStartRecordingAsync();
                  this.resetCurrentTagIfNeeded();
                }}
                onPressOut={() => {
                  this.onActionsStopRecordingAsync(true);
                  this.resetCurrentTagIfNeeded();
                }}>
                <Text
                  style={{
                    fontSize: getCurrentUser().getSmallFontSize()
                  }}>
                  {i18n2.t('PressAndHold')}
                </Text>
              </TouchableOpacity>
            ) : null}
            {this.state.isTextMode ? (
              <View style={{ flexDirection: 'column' }}>
                <TextInput
                  key={this.chatId}
                  // Note: this.setState({ message }); doesn't work in the current set up - iOS Speech-To-Text doesn't work with 'defaultValue' or 'value' in <TextInput>
                  ref={(input) => {
                    if (input && this.textInput !== input) {
                      this.textInput = input;
                      console.log('set initial message ', this.state.message);
                      input.setNativeProps({ text: this.state.message });
                    }
                  }}
                  style={{
                    borderWidth: 1,
                    borderRadius: 7,
                    borderColor: Colors.darkBlue,
                    paddingHorizontal: 9,
                    paddingVertical: 6,
                    fontSize: getCurrentUser().getSmallFontSize()
                  }}
                  multiline={true}
                  placeholder={disablePostMessage ? '' : i18n2.t('TypeMsg')}
                  onFocus={() => {
                    if (this.state.showAccessory) {
                      this.hideAccessory();
                    }

                    this.resetCurrentTagIfNeeded();

                    //scroll to bottom when keyboard shows up
                    this.giftedChat?.scrollToBottom();
                  }}
                  onChange={(e) => {
                    if (e && e.nativeEvent) {
                      if (e.nativeEvent.contentSize) {
                        const textInputHeight = e.nativeEvent.contentSize.height;
                        if (this.state.textInputHeight !== textInputHeight) {
                          this.setState({ textInputHeight });
                        }
                      }

                      if (e.nativeEvent.text !== undefined) {
                        this.setState({ message: e.nativeEvent.text });
                        getCurrentUser().resetPropertyAsync(`chat.draft.${this.chatId}`, e.nativeEvent.text);
                      }
                    }
                  }}
                  onContentSizeChange={(e) => {
                    if (e && e.nativeEvent && e.nativeEvent.contentSize) {
                      const textInputHeight = e.nativeEvent.contentSize.height;
                      if (this.state.textInputHeight !== textInputHeight) {
                        this.setState({ textInputHeight });
                      }
                    }
                  }}
                  onSelectionChange={(e) => {
                    this.cursorPos = e.nativeEvent.selection.end;
                  }}
                  onKeyPress={(e) => {
                    if (e.nativeEvent.key === '@' && typeof this.props.onPeopleAt === 'function') {
                      this.props.onPeopleAt(
                        (user) => {
                          setTimeout(() => {
                            this.addMentionedUser(user);
                          }, 500);
                        },
                        () => {
                          setTimeout(() => {
                            this.textInput?.focus();
                          }, 500);
                        }
                      );
                    }
                  }}
                  enablesReturnKeyAutomatically
                  underlineColorAndroid='transparent'
                />
                {showReply ? (
                  <View
                    style={{
                      height: replyHeight,
                      backgroundColor: '#A0A0A0',
                      margin: 1,
                      borderRadius: 5,
                      justifyContent: 'center'
                    }}>
                    <Text
                      style={{
                        fontSize: 12,
                        color: 'white',
                        padding: (replyHeight - 12) / 2,
                        paddingRight: 30
                      }}
                      numberOfLines={1}>
                      {this.state.replyMessageText.replace(/\n/g, ' ')}
                    </Text>
                    <View
                      style={{
                        position: 'absolute',
                        top: (replyHeight - 24) / 2,
                        right: (replyHeight - 24) / 2
                      }}>
                      <TouchableOpacity
                        activeOpacity={1}
                        onPress={() => {
                          this.setState({ replyMessageId: -1, replyMessageText: '' });
                          getCurrentUser().resetPropertyAsync(`chat.draft.replyId.${this.chatId}`, -1);
                          getCurrentUser().resetPropertyAsync(`chat.draft.replyText.${this.chatId}`, '');

                          this.resetCurrentTagIfNeeded();
                        }}>
                        {getImage('close2', { width: 24, height: 24 })}
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : null}
              </View>
            ) : null}
          </View>
          {this.state.message.length === 0 ? (
            <View style={{ bottom: 3, opacity: disablePostMessage ? 0.1 : 1 }}>
              <TouchableOpacity
                activeOpacity={1}
                style={{ paddingHorizontal: 7, bottom: 3 }}
                onPress={() => {
                  if (this.state.isTextMode) {
                    Keyboard.dismiss();
                  }
                  this.setState({ isTextMode: !this.state.isTextMode, replyMessageId: -1, replyMessageText: '' });
                  getCurrentUser().resetPropertyAsync(`chat.draft.replyId.${this.chatId}`, -1);
                  getCurrentUser().resetPropertyAsync(`chat.draft.replyText.${this.chatId}`, '');

                  this.resetCurrentTagIfNeeded();
                }}>
                {getImage(this.state.isTextMode ? 'voiceButton' : 'keyboard')}
              </TouchableOpacity>
            </View>
          ) : null}
          {this.state.message.length > 0 ? (
            <View style={{ bottom: 3 }}>
              <TouchableOpacity
                activeOpacity={1}
                style={{ paddingHorizontal: 7, bottom: 3 }}
                onPress={async () => {
                  await this.sendMessageAsync();
                  this.resetCurrentTagIfNeeded();
                }}>
                <MaterialCommunityIcons color='black' name='send' size={32} />
              </TouchableOpacity>
            </View>
          ) : null}
        </View>
      </>
    );
  };

  fmRenderInputBar = () => {
    const offset = Platform.select({
      ios: 24,
      android: 20
    });
    const showReply = this.state.isTextMode && this.state.replyMessageId !== -1;
    const inputBarHeight = Math.min(this.state.textInputHeight + offset, Dimensions.get('window').height / 3);
    const bottomOffset = this.context.insets.bottom;
    const screenWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right;
    const replyHeight = this.state.isTextMode ? 30 : 0;
    // hide input bar
    const disablePostMessage = !this.isGroupLeader && !!this.group.onlyLeaderCanPost;
    const fmStyles = getChatUIStyles(disablePostMessage, replyHeight);
    return (
      <>
        <View
          style={[
            fmStyles.fmInputBarContainerBase,
            {
              left: this.context.insets.left,
              width: screenWidth,
              height: inputBarHeight + (showReply ? replyHeight : 0),
              marginVertical: this.state.keyboardShown ? 0 : bottomOffset,
              marginTop: offset
            }
          ]}>
          {disablePostMessage && (
            <View
              style={[
                fmStyles.fmInputBarDisabledOverlay,
                {
                  width: screenWidth,
                  height: inputBarHeight + bottomOffset,
                  bottom: -bottomOffset
                }
              ]}>
              <View
                style={[
                  fmStyles.fmInputBarDisabledInner,
                  {
                    width: screenWidth,
                    height: inputBarHeight
                  }
                ]}>
                <FmText style={fmStyles.fmInputBarDisabledText}>{i18n2.t('ChatScreen.DisableMembersChat')}</FmText>
              </View>
            </View>
          )}
          <View style={fmStyles.fmInputBarPlusIconContainer}>
            <FmPlusIcon
              size={30}
              onPress={() => {
                if (this.state.showAccessory) {
                  this.hideAccessory();
                } else {
                  this.showAccessory(bottomOffset);
                }

                this.resetCurrentTagIfNeeded();
              }}
            />
          </View>
          <View
            style={[
              fmStyles.fmInputBarLeftActionWrapper,
              {
                opacity: disablePostMessage ? 0.1 : 1
              }
            ]}>
            {!this.state.isTextMode ? (
              <TouchableOpacity
                style={fmStyles.fmInputBarVoiceButton}
                onPressIn={() => {
                  this.onActionsStartRecordingAsync();
                  this.resetCurrentTagIfNeeded();
                }}
                onPressOut={() => {
                  this.onActionsStopRecordingAsync(true);
                  this.resetCurrentTagIfNeeded();
                }}>
                <FmText
                  style={{
                    fontSize: getCurrentUser().getSmallFontSize()
                  }}>
                  {i18n2.t('PressAndHold')}
                </FmText>
              </TouchableOpacity>
            ) : null}
            {this.state.isTextMode ? (
              <View style={{ flexDirection: 'column' }}>
                <InputField
                  key={this.chatId}
                  // Note: this.setState({ message }); doesn't work in the current set up - iOS Speech-To-Text doesn't work with 'defaultValue' or 'value' in <TextInput>
                  ref={(input) => {
                    if (input && this.textInput !== input) {
                      this.textInput = input;
                      console.log('set initial message ', this.state.message);
                      input.setNativeProps({ text: this.state.message });
                    }
                  }}
                  value={this.state.message}
                  multiline={true}
                  placeholder={disablePostMessage ? '' : i18n2.t('TypeMsg')}
                  onFocus={() => {
                    if (this.state.showAccessory) {
                      this.hideAccessory();
                    }

                    this.resetCurrentTagIfNeeded();

                    //scroll to bottom when keyboard shows up
                    this.giftedChat?.scrollToBottom();
                  }}
                  onChangeText={(text) => {
                    this.setState({ message: text });
                    getCurrentUser().resetPropertyAsync(`chat.draft.${this.chatId}`, text);
                  }}
                  onContentSizeChange={(e) => {
                    if (e && e.nativeEvent && e.nativeEvent.contentSize) {
                      const textInputHeight = e.nativeEvent.contentSize.height;
                      if (this.state.textInputHeight !== textInputHeight) {
                        this.setState({ textInputHeight });
                      }
                    }
                  }}
                  onSelectionChange={(e) => {
                    this.cursorPos = e.nativeEvent.selection.end;
                  }}
                  onKeyPress={(e) => {
                    if (e.nativeEvent.key === '@' && typeof this.props.onPeopleAt === 'function') {
                      this.props.onPeopleAt(
                        (user) => {
                          setTimeout(() => {
                            this.addMentionedUser(user);
                          }, 500);
                        },
                        () => {
                          setTimeout(() => {
                            this.textInput?.focus();
                          }, 500);
                        }
                      );
                    }
                  }}
                  enablesReturnKeyAutomatically={true}
                  underlineColorAndroid='transparent'
                />
                {showReply ? (
                  <View style={fmStyles.fmInputBarReplyContainer}>
                    <FmText style={fmStyles.fmReplyText} numberOfLines={1}>
                      {this.state.replyMessageText.replace(/\n/g, ' ')}
                    </FmText>
                    <View style={fmStyles.fmReplyCloseIconWrapper}>
                      <BorderlessButton
                        onPress={() => {
                          this.setState({ replyMessageId: -1, replyMessageText: '' });
                          getCurrentUser().resetPropertyAsync(`chat.draft.replyId.${this.chatId}`, -1);
                          getCurrentUser().resetPropertyAsync(`chat.draft.replyText.${this.chatId}`, '');

                          this.resetCurrentTagIfNeeded();
                        }}>
                        {getImage('close2', { width: 24, height: 24 })}
                      </BorderlessButton>
                    </View>
                  </View>
                ) : null}
              </View>
            ) : null}
          </View>
          {this.state.message.length === 0 ? (
            <View style={fmStyles.fmInputBarToggleWrapper}>
              <Pressable
                style={fmStyles.fmInputBarToggleButton}
                onPress={() => {
                  if (this.state.isTextMode) {
                    Keyboard.dismiss();
                  }
                  this.setState({ isTextMode: !this.state.isTextMode, replyMessageId: -1, replyMessageText: '' });
                  getCurrentUser().resetPropertyAsync(`chat.draft.replyId.${this.chatId}`, -1);
                  getCurrentUser().resetPropertyAsync(`chat.draft.replyText.${this.chatId}`, '');

                  this.resetCurrentTagIfNeeded();
                }}>
                {getImage(this.state.isTextMode ? 'voiceButton' : 'keyboard')}
              </Pressable>
            </View>
          ) : null}
          {this.state.message.length > 0 ? (
            <View style={fmStyles.fmInputBarSendWrapper}>
              <RectButton
                style={fmStyles.fmInputBarSendButton}
                onPress={async () => {
                  await this.sendMessageAsync();
                  this.resetCurrentTagIfNeeded();
                }}>
                <FmSendIcon size={32} />
              </RectButton>
            </View>
          ) : null}
        </View>
      </>
    );
  };

  renderTags = ({ currentTag, tags, getTagDisplayName, onAllPress, onTagPress }) => {
    if (this.useNewFav) {
      tags = ['MyFavorite'];
      if (!this.group.isOneOnOneGroup) {
        tags.push('GroupFavorite');
      }
    }

    return (
      <View
        style={{
          position: isFishMeet ? '' : 'absolute',
          left: this.context.insets.left,
          width: Dimensions.get('window').width - this.context.insets.left - this.context.insets.right,
          flexDirection: 'row',
          backgroundColor: 'transparent'
        }}>
        {onAllPress && !isFishMeet ? (
          <TagButton selected={currentTag === undefined} isAllTag={true} onPress={onAllPress} />
        ) : null}
        <ScrollView horizontal>
          {tags.map((tag) => (
            <TagButton
              key={tag}
              id={tag}
              displayName={getTagDisplayName(tag)}
              selected={currentTag === tag}
              onPress={onTagPress}
            />
          ))}
          <View style={{ width: 100 }} />
        </ScrollView>
      </View>
    );
  };

  addMentionedUser = (user, insertAtChat = false) => {
    let { message } = this.state;
    let atChar = '';
    if (insertAtChat) {
      atChar = '@';
    }
    const addedString = `${atChar}${user.name} `;
    message = `${message.substr(0, this.cursorPos)}${addedString}${message.substr(this.cursorPos)}`;
    this.textInput?.setNativeProps({ text: message });
    if (Platform.OS === 'android') {
      //on Android, setNativeProps doesn't not trigger change event to update message
      this.setState({ message });
    }
    getCurrentUser().resetPropertyAsync(`chat.draft.${this.chatId}`, message);
    this.textInput?.focus();

    if (user.isAllUser) {
      if (Array.isArray(user.users)) {
        this.mentionedUsers = user.users;
      }
    } else if (this.mentionedUsers.findIndex((item) => item.userId === user.userId) === -1) {
      // only add when necessary
      this.mentionedUsers.push(user);
    }
    getCurrentUser().resetPropertyAsync(`chat.draft.mentionedUsers.${this.chatId}`, this.mentionedUsers);
  };

  renderBottom = (bottomOffset) => {
    const items = [];

    items.push({
      icon: 'photo',
      text: i18n2.t('Common.Photo'),
      onPress: () => {
        pickImageAsync(this.sendAttachmentAsync);
        this.resetCurrentTagIfNeeded();
      }
    });

    items.push({
      icon: 'camera',
      text: i18n2.t('Common.Camera'),
      onPress: () => {
        takePictureAsync(this.sendAttachmentAsync);
        this.resetCurrentTagIfNeeded();
      }
    });

    items.push({
      icon: 'file',
      text: i18n2.t('Common.File'),
      onPress: () => {
        pickFileAsync(this.sendAttachmentAsync);
        this.resetCurrentTagIfNeeded();
      }
    });

    if (!isFishMeet && this.enableAudioChat) {
      items.push({
        icon: 'conference',
        text: i18n2.t('MeetingUI.Title'),
        onPress: () => {
          launchMeetingAsync(this.groupId, this.context.canOpenJitsi, this.context.canOpenFishMeet);
        }
      });
    }

    let keyIndex = 0;
    return (
      <>
        <View style={{ height: this.accessoryY }}>
          <View
            style={{
              top: this.state.showAccessory ? -bottomOffset : 12,
              paddingTop: 12,
              flexDirection: 'row',
              justifyContent: 'space-around'
            }}>
            {items.map((item) => (
              <View key={keyIndex++}>
                <TouchableOpacity activeOpacity={1} onPress={item.onPress}>
                  {getImage(item.icon)}
                  <Text
                    style={{
                      marginTop: 2,
                      fontSize: getCurrentUser().getSmallFontSize(),
                      textAlign: 'center'
                    }}>
                    {item.text}
                  </Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>

        <View
          pointerEvents='box-none'
          style={{
            flex: 1,
            backgroundColor: '#20202080',
            opacity: 0, // animatedShadowOpacity
            position: 'absolute',
            top: 0,
            bottom: 0,
            left: 0,
            right: 0
          }}>
          {this.state.showBottomSheet ? (
            <TouchableWithoutFeedback onPress={() => this.closeBottomSheet()}>
              <View
                style={{
                  position: 'absolute',
                  top: 0,
                  bottom: 0,
                  left: 0,
                  right: 0
                }}
              />
            </TouchableWithoutFeedback>
          ) : null}
        </View>
      </>
    );
  };

  getModalItems = () => {
    if (!this.longPressSelectedMessage) {
      return { items: [], tagItems: [] };
    }

    const isOwnerOfCurrentMessage = this.longPressSelectedMessage.user._id === this.userId;
    const items = [];

    if (this.isGroupLeader || isOwnerOfCurrentMessage) {
      items.push({
        renderIcon: () => getImage('close'),
        text: i18n2.t('Delete'),
        onPress: async () => {
          await this.chatServer.deleteMessageAsync(this.longPressSelectedMessage._id);
          this.longPressSelectedMessage = null;
        }
      });
    }

    const message = this.longPressSelectedMessage.text;
    const isText =
      !message.startsWith(imagePrefix) &&
      !message.startsWith(filePrefix) &&
      !message.startsWith(audioPrefix) &&
      !message.startsWith(contactPrefix);
    if (isText) {
      items.push({
        renderIcon: () => getImage('copy'),
        text: i18n2.t('Copy'),
        onPress: () => {
          Clipboard.setStringAsync(this.longPressSelectedMessage.text);
        }
      });
    }

    if (!this.shareInGroups) {
      // don't show these two items in discussion group
      items.push({
        renderIcon: () => getImage('groupWithName'),
        text: i18n2.t('ShareToGroup'),
        onPress: () => {
          this.shareToGroupAsync(false);
        }
      });

      items.push({
        renderIcon: () => getImage('groupWithNoName'),
        text: i18n2.t('ShareToGroupNLAnon'),
        onPress: () => {
          this.shareToGroupAsync(true);
        }
      });
    }

    if (isText && this.showInput) {
      items.push({
        renderIcon: () => getImage('reply'),
        text: i18n2.t('Reply'),
        onPress: () => {
          const replyMessageId = this.longPressSelectedMessage.messageId;
          const replyMessageText = `${this.longPressSelectedMessage.user.name}: ${this.longPressSelectedMessage.message}`;
          this.setState({
            isTextMode: true,
            replyMessageId,
            replyMessageText
          });
          getCurrentUser().resetPropertyAsync(`chat.draft.replyId.${this.chatId}`, replyMessageId);
          getCurrentUser().resetPropertyAsync(`chat.draft.replyText.${this.chatId}`, replyMessageText);

          this.resetCurrentTagIfNeeded();

          setTimeout(() => {
            this.textInput?.focus();
          }, 500);
        }
      });
    }

    const tagItems = [];
    if (this.enableTagging) {
      if (this.useNewFav) {
        const { currentTag } = this.state;
        const hasMyFavTag = currentTag === 'MyFavorite';
        tagItems.push({
          hasTag: hasMyFavTag,
          text: `[${i18n2.t('MyFavorite')}]`,
          onPress: async () => {
            if (hasMyFavTag) {
              await this.removeCollectionAsync('user');
            } else {
              await this.addCollectionAsync('user');
            }
          }
        });

        if (this.isGroupLeader) {
          const hasGroupFavTag = currentTag === 'GroupFavorite';
          tagItems.push({
            hasTag: hasGroupFavTag,
            text: `[${i18n2.t('GroupFavorite')}]`,
            onPress: async () => {
              if (hasGroupFavTag) {
                await this.removeCollectionAsync('group');
              } else {
                await this.addCollectionAsync('group');
              }
            }
          });
        }
      } else {
        const tags = (this.longPressSelectedMessage.tags || '').split(' ');
        if (this.isGroupLeader || isOwnerOfCurrentMessage) {
          this.addTag(tagItems, tags, '#NeedPrayer');
        }

        if (this.isGroupLeader) {
          this.addTag(tagItems, tags, '#OnlineInfo');
          this.addTag(tagItems, tags, this.group.isOrgGroup ? '#OrgFavorite' : '#GroupFavorite');
        }
      }
    }

    return { items, tagItems };
  };

  iDigestRenderModal = () => {
    if (!this.longPressSelectedMessage) {
      return null;
    }
    const { items, tagItems } = this.getModalItems();
    const marginHorizontal = 3;
    const padding = 10;
    const iconsPerRow = items.length > 5 ? 5 : items.length;
    const borderWidth = 1;
    const screenWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right - 5;
    const dialogWidth = screenWidth - (padding + marginHorizontal + borderWidth) * 2;
    let keyIndex = 0;

    return (
      <ModalPopup
        insets={this.context.insets}
        visible={this.state.showModal}
        setNotVisible={() => this.setState({ showModal: false })}
        items={items}
        showDivider={tagItems.length === 0}
        iconsPerRow={iconsPerRow}
        showCancel={true}
        hideOnPress={true}>
        {tagItems.length > 0 ? (
          <>
            <View
              style={{
                marginVertical: 10,
                height: 1,
                width: dialogWidth,
                backgroundColor: '#cdcdcd'
              }}
            />
            <View
              style={{
                width: dialogWidth,
                alignItems: 'center'
              }}>
              {getImage('hashtag2')}
            </View>
          </>
        ) : null}
        <View
          style={{
            width: dialogWidth,
            flexDirection: 'row',
            justifyContent: 'center'
          }}>
          {tagItems.map((item) => (
            <View style={{ margin: 5 }} key={keyIndex++}>
              <TouchableOpacity
                activeOpacity={1}
                onPress={() => {
                  item.onPress();
                  this.setState({ showModal: false });
                }}>
                <Text
                  style={{
                    fontSize: 12,
                    paddingTop: 8,
                    textAlign: 'center',
                    textDecorationLine: item.hasTag ? 'line-through' : 'none',
                    textDecorationColor: 'red'
                  }}>
                  {item.text}
                </Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ModalPopup>
    );
  };

  fmRenderModal = () => {
    if (!this.longPressSelectedMessage) {
      return null;
    }
    const { items, tagItems } = this.getModalItems();
    const marginHorizontal = 3;
    const padding = 10;
    const iconsPerRow = items.length > 5 ? 5 : items.length;
    const borderWidth = 1;
    const screenWidth = Dimensions.get('window').width - this.context.insets.left - this.context.insets.right - 5;
    const dialogWidth = screenWidth - (padding + marginHorizontal + borderWidth) * 2;
    const fmStyles = getChatUIStyles();
    let keyIndex = 0;

    return (
      <ModalPopup
        insets={this.context.insets}
        visible={this.state.showModal}
        setNotVisible={() => this.setState({ showModal: false })}
        items={items}
        showDivider={tagItems.length === 0}
        iconsPerRow={iconsPerRow}
        showCancel={false}
        hideOnPress={true}>
        {tagItems.length > 0 ? (
          <>
            <View style={[fmStyles.fmModalDivider, { width: dialogWidth }]} />
          </>
        ) : null}
        <View style={[fmStyles.fmModalTagContainer, { width: dialogWidth }]}>
          {tagItems.map((item) => (
            <View style={{ margin: 5 }} key={keyIndex++}>
              <RectButton
                key={keyIndex++}
                onPress={() => {
                  item.onPress();
                  this.setState({ showModal: false });
                }}>
                <FmTagBaseButton selected={item.hasTag} text={item.text} />
              </RectButton>
            </View>
          ))}
        </View>
      </ModalPopup>
    );
  };

  renderSearchBox = () => {
    const { showSearchBox, searchText } = this.state;
    const fmStyles = getChatUIStyles();

    if (!showSearchBox) {
      return null;
    }

    if (isFishMeet) {
      return (
        <View>
          <InputField
            value={searchText}
            placeholder={i18n2.t('EnterSearch')}
            inputContainerStyle={fmStyles.searchBarInputContainer}
            showClearButton={true}
            onClearButton={() => {
              this.setState({ searchText: '', messages: [], showSearchBox: false });
              this.chatServer.getMessagesAsync({});
            }}
            onChangeText={(text) => {
              this.setState({ searchText: text });
            }}
            onSubmitEditing={() => {
              Keyboard.dismiss();
              const trimmed = searchText.trim();
              this.setState({ searchText: trimmed, messages: [] });
              this.chatServer.getMessagesAsync({ search: trimmed });
            }}
          />
        </View>
      );
    }

    return (
      <View style={{ marginTop: 40 + getCurrentUser().getFontSize() }}>
        <SearchBar
          autoFocus={true}
          isCloseVisible={false}
          onChangeText={(searchText) => {
            this.setState({ searchText });
          }}
          enablesReturnKeyAutomatically={true}
          onSubmitEditing={(evt) => {
            Keyboard.dismiss();
            const text = evt.nativeEvent.text.trim();
            this.setState({ searchText: text, messages: [] });
            this.chatServer.getMessagesAsync({ search: text });
          }}
        />
      </View>
    );
  };

  renderSearchButton = () => {
    const { showSearchBox, searchText } = this.state;
    const TagBaseButtonComponent = isFishMeet ? FmSearchButton : TagBaseButton;
    return (
      <View
        style={{
          position: 'absolute',
          right: this.context.insets.right,
          flexDirection: 'row',
          backgroundColor: 'transparent'
        }}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            if (showSearchBox) {
              if (!searchText) {
                // return
                this.setState({ showSearchBox: false, messages: [], currentTag: undefined });
                this.chatServer.getMessagesAsync({});
              } else {
                // execute search
                this.chatServer.getMessagesAsync({ search: searchText });
              }
            } else {
              this.setState({ showSearchBox: true, messages: [], currentTag: '__search_in_the_action__' });
              if (searchText) {
                // execute search
                this.chatServer.getMessagesAsync({ search: searchText });
              }
            }
          }}>
          <TagBaseButtonComponent
            selected={showSearchBox}
            fontWeight={showSearchBox ? 'bold' : 'normal'}
            text={i18n2.t('Search')}
          />
        </TouchableOpacity>
      </View>
    );
  };

  renderImagePreview() {
    const { messages, currentImageIndex } = this.state;
    const images = messages
      ? messages
          .filter((item) => item.text && item.text.startsWith(imagePrefix))
          .reverse()
          .map((item) => {
            const temp = item.text.split('!') || [];
            return { uri: temp[2] || '', ...item };
          })
      : [];

    return (
      <ImagePreview
        imageIndex={currentImageIndex}
        images={images}
        ableSave
        ableShare
        onRequestClose={() => {
          this.setState({ currentImageIndex: -1 });
        }}
      />
    );
  }

  iDigestRenderChatUI() {
    const { messages } = this.state;
    const currentUser = getCurrentUser();

    return {
      renderDay: (props) => {
        const { currentMessage, previousMessage, wrapperStyle } = props;
        if (!currentMessage || isSameDayMessage(currentMessage, previousMessage)) {
          return null;
        }
        const d = new Date(currentMessage.createdAt);
        const isFirstMessage = messages.length > 0 ? currentMessage._id === messages[messages.length - 1]._id : false;
        return (
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: isFirstMessage ? 40 : 7
            }}>
            <View style={wrapperStyle}>
              <Text
                style={{
                  color: '#b2b2b2',
                  fontSize: currentUser.getXSmallFontSize(),
                  fontWeight: '600'
                }}>
                {d.toLocaleString(currentUser.getLocale(), {
                  weekday: 'short',
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
              </Text>
            </View>
          </View>
        );
      },

      renderMessageText: (props) => (
        <MessageText
          {...props}
          textStyle={{
            left: {
              color: '#202020',
              fontSize: currentUser.getSmallFontSize(),
              lineHeight: currentUser.getSmallFontSize() * 1.25
            },
            right: {
              color: '#202020',
              fontSize: currentUser.getSmallFontSize(),
              lineHeight: currentUser.getSmallFontSize() * 1.25
            }
          }}
          linkStyle={{
            left: { color: '#202020' },
            right: { color: '#202020' }
          }}
        />
      ),

      renderAvatar: (e) => {
        const user = e.currentMessage.user;
        if (isFishMeet && user._id === this.userId) {
          return null;
        }
        // hide member detail exclude self and group leader
        const disableShowMemberDetail =
          !!this.group.disableMemberShowList &&
          !this.isGroupLeader &&
          user._id !== this.userId &&
          !this.props.leaderIds.includes(user._id);
        if (!user.email || disableShowMemberDetail) {
          return <AvatarIcon size={40} fontSize={currentUser.getXSmallFontSize()} isAnonymous={true} />;
        }

        return (
          <Avatar2
            userId={user._id}
            onPress={() => {
              if (this.props.enableUserInfo) {
                this.props.navigation.navigate('Member', {
                  id: user._id,
                  uniqueId: user.email,
                  name: user.name,
                  email: user.email,
                  hideChat: !!this.group.isOneOnOneGroup,
                  userTag: this.context.userTags[user._id] || '',
                  isUserBlocked: this.context.blocks[user._id] || false,
                  isCurrentUserBlocking: this.context.blockedBy.includes(user._id)
                });
              }
            }}
            onLongPress={() => {
              if (this.props.enableUserInfo) {
                this.addMentionedUser(user, true);
              }
            }}
          />
        );
      }
    };
  }

  fmRenderChatUI() {
    const { messages } = this.state;
    const currentUser = getCurrentUser();
    const fmStyles = getChatUIStyles();

    return {
      renderDay: (props) => {
        const { currentMessage, previousMessage, wrapperStyle } = props;
        if (!currentMessage || isSameDayMessage(currentMessage, previousMessage)) {
          return null;
        }
        const d = new Date(currentMessage.createdAt);
        const isFirstMessage = messages.length > 0 ? currentMessage._id === messages[messages.length - 1]._id : false;
        return (
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: isFirstMessage ? 40 : 7
            }}>
            <View style={wrapperStyle}>
              <FmText style={fmStyles.dayText}>
                {d.toLocaleString(currentUser.getLocale(), {
                  weekday: 'short',
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}
              </FmText>
            </View>
          </View>
        );
      },

      renderMessageText: (props) => (
        <MessageText
          {...props}
          textStyle={{
            left: fmStyles.messageTextLeft,
            right: fmStyles.messageTextRight
          }}
          linkStyle={{
            left: fmStyles.linkText,
            right: fmStyles.linkText
          }}
        />
      ),

      renderAvatar: (e) => {
        const user = e.currentMessage.user;
        if (isFishMeet && user._id === this.userId) {
          return null;
        }
        // hide member detail exclude self and group leader
        const disableShowMemberDetail =
          !!this.group.disableMemberShowList &&
          !this.isGroupLeader &&
          user._id !== this.userId &&
          !this.props.leaderIds.includes(user._id);
        if (!user.email || disableShowMemberDetail) {
          return <AvatarIcon size={40} fontSize={currentUser.getXSmallFontSize()} isAnonymous={true} />;
        }

        return (
          <Avatar2
            userId={user._id}
            onPress={() => {
              if (this.props.enableUserInfo) {
                this.props.navigation.navigate('Member', {
                  id: user._id,
                  uniqueId: user.email,
                  name: user.name,
                  email: user.email,
                  hideChat: !!this.group.isOneOnOneGroup,
                  userTag: this.context.userTags[user._id] || '',
                  isUserBlocked: this.context.blocks[user._id] || false,
                  isCurrentUserBlocking: this.context.blockedBy.includes(user._id)
                });
              }
            }}
            onLongPress={() => {
              if (this.props.enableUserInfo) {
                this.addMentionedUser(user, true);
              }
            }}
          />
        );
      }
    };
  }

  renderChat() {
    const { messages } = this.state;
    const currentUser = getCurrentUser();
    const { renderDay, renderMessageText, renderAvatar } = isFishMeet
      ? this.fmRenderChatUI()
      : this.iDigestRenderChatUI();

    return (
      <GiftedChat
        key={this.props.leaderIds.join('') || 'giftedChatKey'}
        ref={(input) => {
          this.giftedChat = input;
        }}
        messages={messages}
        listViewProps={{
          scrollEventThrottle: 400,
          onScroll: async ({ nativeEvent }) => {
            if (this.isNearTop(nativeEvent) && !this.state.busy) {
              await this.loadEarlierMessagesAsync();
            }

            // dismiss keyboard while scrolling - same behavior as WeChat
            if (Keyboard.isVisible()) {
              Keyboard.dismiss();
            }
          }
        }}
        renderLoading={() => <LoadingIndicator />}
        isAnimated={true}
        showAvatarForEveryMessage={true}
        showUserAvatar={true}
        user={{
          _id: this.userId,
          name: currentUser.nickname
        }}
        renderDay={renderDay}
        renderBubble={(props) => {
          const isSelf = props.currentMessage.user._id === this.userId;
          props.isSelf = isSelf;
          props.enableUserInfo = this.props.enableUserInfo;
          // hide member detail exclude self and group leader
          const disableShowMemberDetail =
            !!this.group.disableMemberShowList &&
            !this.isGroupLeader &&
            !isSelf &&
            !this.props.leaderIds.includes(props.currentMessage.user._id);
          props.showUserName = !isSelf && !this.group.isOneOnOneGroup && !disableShowMemberDetail;
          props.blocks = this.context.blocks;
          props.blockedBy = this.context.blockedBy;
          props.navigation = this.props.navigation;
          props.enableTaggingLink = this.enableTaggingLink;
          props.onLongPress = () => {
            this.onLongPress(props.currentMessage);
          };
          props.onTagPress = (tag) => {
            this.onTagPress(tag);
          };
          props.onTagLongPress = () => {
            this.onLongPress(props.currentMessage);
          };

          props.userTags = this.context.userTags;

          const message = props.currentMessage.text;
          if (message.startsWith(audioPrefix)) {
            return this.renderAudioBubble(props);
          }

          if (message.startsWith(filePrefix)) {
            return this.renderFileBubble(props);
          }

          if (message.startsWith(imagePrefix)) {
            return this.renderImageBubble(props);
          }

          if (message.startsWith(contactPrefix)) {
            return this.renderContactBubble(props);
          }

          // 新逻辑：判断是否包含programDetail的shareurl
          if (message.match(/https?:\/\/[^ \n]*#\/programDetail[^ \n]*/g)) {
            return this.renderChannelProgram(props);
          }

          return this.renderTextBubble(props);
        }}
        renderMessageText={renderMessageText}
        renderAvatarOnTop={true}
        renderAvatar={renderAvatar}
        onLongPress={(context, message) => {
          this.onLongPress(message);
        }}
        isKeyboardInternallyHandled={false}
        keyboardShouldPersistTaps='handled'
        minInputToolbarHeight={0}
        renderInputToolbar={(props) => null}
      />
    );
  }

  renderTagFilters() {
    const { currentTag, tags } = this.state;

    return (
      <>
        {this.enableTagging &&
          this.renderTags({
            currentTag,
            tags,
            getTagDisplayName: (tag) => tag,
            onAllPress: () => {
              this.setState({ currentTag: undefined, messages: [], showSearchBox: false });
              this.chatServer.getMessagesAsync({});
            },
            onTagPress: (tag) => {
              if (isFishMeet) {
                if (this.state.currentTag === tag) {
                  this.setState({ currentTag: undefined, messages: [], showSearchBox: false });
                  this.chatServer.getMessagesAsync({});
                } else {
                  this.setState({ currentTag: tag, messages: [], showSearchBox: false });
                  this.chatServer.getMessagesAsync({ tag, showSearchBox: false });
                }
              } else {
                this.setState({ currentTag: tag, messages: [], showSearchBox: false });
                this.chatServer.getMessagesAsync({ tag, showSearchBox: false });
              }
            }
          })}
        {this.showGroups.length > 0 &&
          this.renderTags({
            currentTag,
            tags,
            getTagDisplayName: (tag) => {
              if (tag === 0) {
                return i18n2.t('Public');
              }

              const pos = this.showGroups.findIndex((it) => it.groupId === tag);
              if (pos !== -1) {
                const item = this.showGroups[pos];
                return item.name.length > 8 ? item.name.substr(0, 7) + '...' : item.name;
              }

              return tag;
            },
            onTagPress: (tag) => {
              // also clear quote messages
              this.setState({
                currentTag: tag,
                messages: [],
                replyMessageId: -1,
                replyMessageText: '',
                showSearchBox: false
              });
              if (tag === 0) {
                this.shareInGroups = JSON.stringify([0]);
                this.chatServer.getMessagesAsync({ shareInGroups: this.shareInGroups });
              } else {
                const pos = this.showGroups.findIndex((it) => it.groupId === tag);
                if (pos !== -1) {
                  const item = this.showGroups[pos];
                  this.shareInGroups = JSON.stringify([item.groupId]);
                  this.chatServer.getMessagesAsync({ shareInGroups: this.shareInGroups });
                }
              }
            }
          })}
      </>
    );
  }

  renderHeaderTap = () => {
    const fmStyles = getChatUIStyles();
    const groupName = this.group.name || 'Group Name';

    return (
      <View style={fmStyles.chatHeaderContainer}>
        <FmText style={fmStyles.chatHeaderTitle}>{groupName}</FmText>
        <View style={fmStyles.chatHeaderActionsContainer}>
          <RectButton
            style={fmStyles.chatHeaderActionButton}
            onPress={() => {
              launchMeetingAsync(this.groupId, this.context.canOpenJitsi, this.context.canOpenFishMeet);
            }}>
            {getImage('openMeeting')}
          </RectButton>
          <RectButton
            style={fmStyles.chatHeaderActionButton}
            onPress={() => {
              this.props.navigation.navigate('Group', {
                title: this.props.title,
                group: this.group,
                classId: this.props.classId,
                isOrgGroup: this.props.isOrgGroupChat
              });
            }}>
            {getImage('group')}
          </RectButton>
        </View>
      </View>
    );
  };

  iDigestRender() {
    const { showSearchBox } = this.state;

    return (
      <>
        {this.renderImagePreview()}
        {this.renderSearchBox()}
        {this.renderChat()}
        {this.renderTagFilters()}
        {this.showInput && !showSearchBox && this.iDigestRenderInputBar()}
        {this.renderPlay()}
        {this.renderRecording()}
        {this.renderFullScreenMessage()}
        {this.renderLoadingIndicator()}
        {this.showInput && Platform.OS === 'ios' ? <KeyboardSpacer /> : null}
        {this.props.children}
        {this.renderBottom(this.context.insets.bottom)}
        {this.renderSearchButton()}
        {this.iDigestRenderModal()}
      </>
    );
  }

  fmRender() {
    const { showSearchBox } = this.state;

    return (
      <>
        {this.renderImagePreview()}
        {this.renderHeaderTap()}
        {this.renderChat()}
        {!showSearchBox ? (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {this.renderTagFilters()}
            {this.renderSearchButton()}
          </View>
        ) : (
          this.renderSearchBox()
        )}
        {/* {this.showInput && !showSearchBox && this.fmRenderInputBar()} */}

        {this.showInput && this.fmRenderInputBar()}
        {this.renderPlay()}
        {this.renderRecording()}
        {this.renderFullScreenMessage()}
        {this.renderLoadingIndicator()}
        {this.showInput && Platform.OS === 'ios' ? <KeyboardSpacer /> : null}
        {this.props.children}
        {this.renderBottom(this.context.insets.bottom)}
        {this.fmRenderModal()}
      </>
    );
  }

  render() {
    return isFishMeet ? this.fmRender() : this.iDigestRender();
  }

  resetCurrentTagIfNeeded = () => {
    if (this.showGroups.length > 0) {
      // don't reset if we're showing study group as tags
      return;
    }

    if (this.state.currentTag) {
      this.setState({ currentTag: undefined, messages: [], showSearchBox: false });
      this.chatServer.getMessagesAsync({});
      return;
    }
  };

  renderChannelProgram(props) {
    // 只根据内容中是否有programDetail的shareurl来渲染卡片
    let text = props.currentMessage.text || '';
    const lines = text.trim().split('\n');
    let title = '';
    let subtitle = '';
    if (lines.length > 0) {
      title = lines[0].trim();
    }
    if (lines.length > 1) {
      subtitle = lines[1].trim();
    }

    // 2. 匹配带 #/programDetail 的 URL（支持任意域名）
    const regex = /https?:\/\/[^\s]*#\/programDetail[^\s]*/g;
    const shareUrlMatch = text.match(regex);
    if (shareUrlMatch) {
      // 3. 解析 URL 参数
      const urlString = shareUrlMatch[0];

      const queryMatch = urlString.match(/\?(.*)/);
      let channelId;
      let programId;
      let cover;

      if (queryMatch && queryMatch[1]) {
        const queryString = queryMatch[1];

        const queryParts = queryString.split('&');
        for (const part of queryParts) {
          const [key, value] = part.split('=');
          if (key && value) {
            const decodedValue = decodeURIComponent(value);
            if (key === 'channelId') {
              channelId = decodedValue;
            }
            if (key === 'programId') {
              programId = decodedValue;
            }
            if (key === 'cover') {
              cover = decodedValue;
            }
          }
        }
      }
      const previewUrl = cover
        ? getHttpsServer(`/channels/${channelId}/content/${cover}`) + `?programId=${programId}&time=${Date.now()}`
        : undefined;
      return (
        <TouchableOpacity
          activeOpacity={0.8}
          style={{
            flexDirection: 'row',
            backgroundColor: '#f7f7f7',
            borderRadius: 10,
            padding: 10,
            marginVertical: 5,
            alignItems: 'center',
            maxWidth: '80%'
          }}
          onPress={() => {
            this.props.navigation.navigate('ChannelProgram', {
              channelId,
              programId
            });
          }}>
          {previewUrl ? (
            <Image
              contentFit='contain'
              source={{ uri: previewUrl }}
              style={{ width: 60, height: 60, borderRadius: 8, backgroundColor: '#eee', marginRight: 14 }}
            />
          ) : null}
          <View style={{ flex: 1 }}>
            <Text
              numberOfLines={2}
              style={{ fontSize: getFontSize().mediumFontSize, fontWeight: 'bold', color: '#222', marginBottom: 2 }}>
              {title || ''}
            </Text>
            {subtitle ? (
              <Text style={{ fontSize: getFontSize().smallMinusFontSize, color: '#666' }} numberOfLines={1}>
                {subtitle}
              </Text>
            ) : null}
          </View>
        </TouchableOpacity>
      );
    }
    // 找不到则降级为文本气泡
    return this.renderTextBubble(props);
  }
}

const IDigestBubble = (props) => {
  const context = useContext(AppContext);

  const styles = props.isSelf ? rightStyles : leftStyles;
  const fontSize = getCurrentUser().getSmallFontSize();
  const fontSizeXSmall = getCurrentUser().getXSmallFontSize();
  // 'PrayerAnswered' => ''
  const tags = (props.currentMessage.tags || '').replace('#PrayerAnswered ', '').split(' ');
  const d = new Date(props.currentMessage.createdAt);
  const quote = props.currentMessage.quote;
  const showQuote = typeof quote === 'string' && quote.length > 0;
  const [showURLPreview, setShowURLPreview] = useState(0);
  const showUserName = props.showUserName;
  const user = props.currentMessage.user;
  const userTag = props.userTags[user._id] || '';
  const blocks = props.blocks;
  const blockedBy = props.blockedBy;

  return (
    <View style={{ flex: showURLPreview, maxWidth: '75%', marginBottom: showUserName ? 3 : 0 }}>
      {showUserName && (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            if (props.enableUserInfo) {
              props.navigation.navigate('Member', {
                id: user._id,
                uniqueId: user.email,
                name: user.name,
                email: user.email,
                userTag,
                isUserBlocked: blocks[user._id] || false,
                isCurrentUserBlocking: blockedBy.includes(user._id)
              });
            }
          }}>
          <Text style={{ fontSize: fontSizeXSmall, color: Colors.text }}>
            {props.currentMessage.user.name} {userTag ? `(${userTag})` : ''}
          </Text>
        </TouchableOpacity>
      )}
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          if (props.onPress) {
            props.onPress();
          }
        }}
        onLongPress={() => {
          if (props.onLongPress) {
            props.onLongPress();
          }
        }}>
        <View
          style={{
            ...styles,
            borderBottomLeftRadius: showQuote ? 0 : styles.borderBottomLeftRadius,
            borderBottomRightRadius: showQuote ? 0 : styles.borderBottomRightRadius
          }}>
          {tags.length > 0 ? (
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', paddingHorizontal: 5 }}>
              {tags.map((tag) => {
                if (!tag) {
                  return null;
                }
                if (props.enableTaggingLink) {
                  return (
                    <TouchableOpacity
                      activeOpacity={1}
                      key={`${props.currentMessage._id}${tag}`}
                      style={{ marginBottom: fontSizeXSmall }}
                      onPress={() => {
                        if (props.onTagPress) {
                          props.onTagPress(tag.substr(1));
                        }
                      }}
                      onLongPress={() => {
                        if (props.onTagLongPress) {
                          props.onTagLongPress(tag.substr(1));
                        }
                      }}>
                      <Text
                        style={{
                          paddingHorizontal: 2,
                          color: '#202020',
                          fontSize: fontSizeXSmall,
                          fontWeight: 'bold',
                          fontStyle: 'italic'
                        }}>
                        【#{i18n2.t(tag.substr(1) === 'NeedPrayer' ? 'Prayer' : tag.substr(1))}】
                      </Text>
                    </TouchableOpacity>
                  );
                }

                return (
                  <Text
                    key={`${props.currentMessage._id}${tag}`}
                    style={{
                      paddingHorizontal: 2,
                      color: '#202020',
                      fontSize: fontSizeXSmall,
                      fontWeight: 'bold',
                      fontStyle: 'italic',
                      marginBottom: fontSizeXSmall
                    }}>
                    【#{i18n2.t(tag.substr(1) === 'NeedPrayer' ? 'Prayer' : tag.substr(1))}】
                  </Text>
                );
              })}
            </View>
          ) : null}
          {props.text !== undefined ? (
            <>
              <ParsedText
                style={{
                  padding: 5,
                  color: '#202020',
                  fontSize,
                  lineHeight: fontSize * 1.25
                }}
                parse={[
                  {
                    pattern: parsedTextPattern,
                    style: { textDecorationLine: 'underline', color: Colors.darkBlue },
                    onPress: (url) => {
                      openUrl(url, props.navigation, context);
                    },
                    onLongPress: () => {
                      if (props.onLongPress) {
                        props.onLongPress();
                      }
                    }
                  },
                  {
                    pattern: / #NeedPrayer | #祷告事项 | #禱告事項 /,
                    style: { textDecorationLine: 'underline', color: Colors.darkBlue },
                    onPress: () => {
                      props.onTagPress('NeedPrayer');
                    },
                    onLongPress: () => {
                      props.onTagPress('NeedPrayer');
                    }
                  }
                ]}
                childrenProps={{ allowFontScaling: false }}>
                {props.text}
              </ParsedText>
              <RNURLPreview
                text={props.text}
                containerStyle={{
                  padding: 3
                }}
                imageStyle={{ width: 80 }}
                textContainerStyle={{ flex: 1, paddingLeft: 7 }}
                titleStyle={{ fontSize, color: '#202020' }}
                descriptionStyle={{ fontSize: fontSizeXSmall, color: Colors.darkBlue }}
                onLoad={() => setShowURLPreview(1)}
                onPress={(url) => {
                  if (props.onPress) {
                    props.onPress(url);
                  }
                }}
                onLongPress={() => {
                  if (props.onLongPress) {
                    props.onLongPress();
                  }
                }}
              />
            </>
          ) : null}
          {props.children}
          <Text
            style={{
              color: '#202020',
              fontSize: getCurrentUser().getXXSmallFontSize(),
              textAlign: 'right',
              marginLeft: 10,
              marginRight: 10,
              marginBottom: 5
            }}>
            {d.toLocaleString(getCurrentUser().getLocale(), {
              hour: 'numeric',
              minute: 'numeric'
            })}
          </Text>
        </View>
      </TouchableOpacity>
      {showQuote ? (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            if (typeof props.onQuotePress == 'function') {
              props.onQuotePress(props.currentMessage.quote);
            }
          }}>
          <View
            style={{
              ...styles,
              marginTop: 2,
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0,
              backgroundColor: '#A0A0A0'
            }}>
            <ParsedText
              style={{
                padding: 5,
                color: 'white',
                fontSize: fontSizeXSmall,
                lineHeight: fontSizeXSmall * 1.25
              }}
              numberOfLines={2}>
              {quote.replace(/\n/g, ' ')}
            </ParsedText>
          </View>
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

const FmBubble = (props) => {
  const context = useContext(AppContext);

  const styles = props.isSelf ? rightStyles : leftStyles;

  const fontSizeXSmall = getCurrentUser().getXSmallFontSize();
  // 'PrayerAnswered' => ''
  const tags = (props.currentMessage.tags || '').replace('#PrayerAnswered ', '').split(' ');
  const d = new Date(props.currentMessage.createdAt);
  const quote = props.currentMessage.quote;
  const showQuote = typeof quote === 'string' && quote.length > 0;
  const [showURLPreview, setShowURLPreview] = useState(0);
  const showUserName = props.showUserName;
  const user = props.currentMessage.user;
  const userTag = props.userTags[user._id] || '';
  const blocks = props.blocks;
  const blockedBy = props.blockedBy;
  const fmStyles = getChatUIStyles();

  return (
    <View style={{ flex: showURLPreview, maxWidth: '75%', marginBottom: showUserName ? 3 : 0 }}>
      {showUserName && (
        <Pressable
          onPress={() => {
            if (props.enableUserInfo) {
              props.navigation.navigate('Member', {
                id: user._id,
                uniqueId: user.email,
                name: user.name,
                email: user.email,
                userTag,
                isUserBlocked: blocks[user._id] || false,
                isCurrentUserBlocking: blockedBy.includes(user._id)
              });
            }
          }}>
          <FmText style={fmStyles.nameText}>
            {props.currentMessage.user.name}
            {userTag ? ` (${userTag})` : ''}
          </FmText>
        </Pressable>
      )}
      <Pressable
        onPress={() => {
          if (props.onPress) {
            props.onPress();
          }
        }}
        onLongPress={() => {
          if (props.onLongPress) {
            props.onLongPress();
          }
        }}>
        <View
          style={{
            ...styles,
            borderBottomLeftRadius: showQuote ? 0 : styles.borderBottomLeftRadius,
            borderBottomRightRadius: showQuote ? 0 : styles.borderBottomRightRadius
          }}>
          {tags.length > 0 ? (
            <View style={fmStyles.tagContainer}>
              {tags.map((tag) => {
                if (!tag) {
                  return null;
                }
                const displayTag = `【#${i18n2.t(tag.substr(1) === 'NeedPrayer' ? 'Prayer' : tag.substr(1))}】`;
                if (props.enableTaggingLink) {
                  return (
                    <Pressable
                      key={`${props.currentMessage._id}${tag}`}
                      style={{ marginBottom: fontSizeXSmall }}
                      onPress={() => {
                        if (props.onTagPress) {
                          props.onTagPress(tag.substr(1));
                        }
                      }}
                      onLongPress={() => {
                        if (props.onTagLongPress) {
                          props.onTagLongPress(tag.substr(1));
                        }
                      }}>
                      <FmText style={fmStyles.tagText}>{displayTag}</FmText>
                    </Pressable>
                  );
                }

                return (
                  <FmText
                    key={`${props.currentMessage._id}${tag}`}
                    style={[fmStyles.tagText, { marginBottom: fontSizeXSmall }]}>
                    {displayTag}
                  </FmText>
                );
              })}
            </View>
          ) : null}
          {props.text !== undefined ? (
            <>
              <ParsedText
                style={fmStyles.parsedText}
                parse={[
                  {
                    pattern: parsedTextPattern,
                    style: { textDecorationLine: 'underline', color: Colors.darkBlue },
                    onPress: (url) => {
                      openUrl(url, props.navigation, context);
                    },
                    onLongPress: () => {
                      if (props.onLongPress) {
                        props.onLongPress();
                      }
                    }
                  },
                  {
                    pattern: / #NeedPrayer | #祷告事项 | #禱告事項 /,
                    style: { textDecorationLine: 'underline', color: Colors.darkBlue },
                    onPress: () => {
                      props.onTagPress('NeedPrayer');
                    },
                    onLongPress: () => {
                      props.onTagPress('NeedPrayer');
                    }
                  }
                ]}
                childrenProps={{ allowFontScaling: false }}>
                {props.text}
              </ParsedText>
              <RNURLPreview
                text={props.text}
                containerStyle={{
                  padding: 3
                }}
                imageStyle={{ width: 80 }}
                textContainerStyle={fmStyles.urlPreviewTextContainer}
                titleStyle={fmStyles.urlPreviewTitle}
                descriptionStyle={fmStyles.urlPreviewDescription}
                onLoad={() => setShowURLPreview(1)}
                onPress={(url) => {
                  if (props.onPress) {
                    props.onPress(url);
                  }
                }}
                onLongPress={() => {
                  if (props.onLongPress) {
                    props.onLongPress();
                  }
                }}
              />
            </>
          ) : null}
          {props.children}
        </View>
      </Pressable>
      {showQuote ? (
        <Pressable
          onPress={() => {
            if (typeof props.onQuotePress == 'function') {
              props.onQuotePress(props.currentMessage.quote);
            }
          }}>
          <View
            style={{
              ...styles,
              ...fmStyles.quoteContainer
            }}>
            <ParsedText style={fmStyles.quoteParsedText} numberOfLines={2}>
              {quote.replace(/\n/g, ' ')}
            </ParsedText>
          </View>
        </Pressable>
      ) : null}
      <FmText style={fmStyles.timestamp}>
        {d.toLocaleString(getCurrentUser().getLocale(), {
          hour: 'numeric',
          minute: 'numeric'
        })}
      </FmText>
    </View>
  );
};

const TagButton = (props) => {
  const { isAllTag, displayName, selected } = props;
  const tagDisplayName = isAllTag ? 'All' : displayName;
  const i18nTag = i18n2.t(tagDisplayName === 'NeedPrayer' ? 'Prayer' : tagDisplayName);
  const isPublicTag = tagDisplayName === 'Public';
  const TagBaseButtonComponent = isFishMeet ? FmTagBaseButton : TagBaseButton;
  const Wrapper = isFishMeet ? RectButton : TouchableOpacity;

  if (selected && !isFishMeet) {
    return (
      <TagBaseButtonComponent
        selected={selected}
        fontWeight={isAllTag || isPublicTag ? 'bold' : 'normal'}
        text={isAllTag || isPublicTag ? i18nTag : ' #' + i18nTag}
      />
    );
  }

  return (
    <Wrapper
      activeOpacity={1}
      onPress={() => {
        if (typeof props.onPress === 'function') {
          props.onPress(props.id);
        }
      }}>
      <TagBaseButtonComponent
        selected={selected}
        fontWeight={isAllTag || isPublicTag ? 'bold' : 'normal'}
        text={isAllTag || isPublicTag ? i18nTag : ' #' + i18nTag}
      />
    </Wrapper>
  );
};

const TagBaseButton = (props) => (
  <View
    style={{
      borderWidth: 2,
      borderRadius: 24,
      borderColor: 'white',
      marginVertical: 5,
      marginHorizontal: 0.5
    }}>
    <View
      style={{
        padding: 5,
        borderRadius: 24,
        backgroundColor: props.selected ? '#202020' : Colors.buttonBackground,
        borderColor: Colors.buttonBorder,
        borderWidth: 1
      }}>
      <Text
        style={{
          color: props.selected ? 'white' : Colors.buttonText,
          textAlign: 'center',
          fontSize: getCurrentUser().getXSmallFontSize(),
          paddingHorizontal: 8,
          fontWeight: props.fontWeight || 'normal'
        }}>
        {props.text}
      </Text>
    </View>
  </View>
);

const FmTagBaseButton = (props) => {
  const fmStyles = getChatUIStyles();
  return (
    <View style={fmStyles.fmTagBaseButtonOuter}>
      <View
        style={[
          fmStyles.fmTagBaseButtonInner,
          { backgroundColor: props.selected ? FmColors.accent : FmColors.lightAccent }
        ]}>
        <FmText
          style={{
            textAlign: 'center',
            fontSize: getCurrentUser().getXSmallFontSize(),
            paddingHorizontal: 5,
            color: Colors.darkGray
          }}>
          {props.text}
        </FmText>
      </View>
    </View>
  );
};

const FmSearchButton = (props) => {
  const fmStyles = getChatUIStyles();
  return (
    <View style={fmStyles.fmSearchButtonOuter}>
      <View style={fmStyles.fmSearchButtonInner}>
        <FmText
          style={{
            fontSize: getCurrentUser().getXSmallFontSize(),
            paddingRight: FM_SCREEN_PADDING_HORIZONTAL,
            color: props.selected ? FmColors.lightAccent : Colors.darkGray
          }}>
          {props.text}
        </FmText>
        <View>{getImage('search')}</View>
      </View>
    </View>
  );
};

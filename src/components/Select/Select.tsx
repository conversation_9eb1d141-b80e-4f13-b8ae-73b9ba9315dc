/* eslint-disable react-native/no-unused-styles */

import { Keyboard, StyleSheet, Text, TouchableOpacity } from 'react-native';
import React, { useContext, useState } from 'react';

import { AppContext } from '@/context/AppContext';
import { Colors } from '@/styles/colors';
import DefaultSelectItem from '@/components/Select/DefaultSelectItem';
import { FmText } from '@/fishMeet/components/FmText';
import { KeyboardFlatList } from '@/components';
import { RectButton } from 'react-native-gesture-handler';
import SearchBar from '@/components/SearchBar';
import { getCurrentUser } from '@/utils/user';
import { getFmSelectScreenStyles } from '@/fishMeet/styles/screens/selectScreenStyles';
import { isFishMeet } from '@/utils/deviceOrAppType';

export interface ISelect<T> {
  getDisplayName: (item: T) => string;
  choices: T[];
  onSelect: (item: T) => void;
  onRenderItem?: (item: T) => React.ReactNode;
  titleControl?: React.ReactNode;
  text?: string;
  isCurrent?: (item: T) => boolean;
  keyString?: string;
  multiple?: boolean;
  itemIcon?: (item: T) => React.ReactNode;
}

const getSelectScreenStyles = (insets: AppContextType['insets']) =>
  StyleSheet.create({
    screenContainer: {
      paddingLeft: insets.left,
      paddingRight: insets.right
    },
    titleText: {
      marginVertical: 15,
      fontSize: getCurrentUser().getSmallFontSize(),
      color: Colors.darkBlue
    }
  });

type AppContextType = { insets: { top: number; bottom: number; left: number; right: number } };

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const Select = <T extends Record<string, any>>(props: ISelect<T>): React.ReactElement => {
  const choices = props.choices;
  const context = useContext(AppContext) as AppContextType;

  const [searchText, setSearchText] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const handleSearchTextChange = (text: string) => {
    setSearchText(text);
  };

  const handleSearchTextFocus = () => {
    setIsSearching(true);
  };

  const handleSearchTextClose = () => {
    Keyboard.dismiss();
    setIsSearching(false);
    setSearchText('');
  };

  const styles = getSelectScreenStyles(context.insets);
  const fmStyles = getFmSelectScreenStyles(context.insets);

  const searchTextLowerCase = searchText.trim().toLowerCase();
  const showChoices = searchText.trim()
    ? choices.filter((it) => props.getDisplayName(it).toLowerCase().includes(searchTextLowerCase))
    : choices;

  const headerText = isFishMeet ? (
    <FmText style={fmStyles.titleText}>{props?.text}</FmText>
  ) : (
    <Text style={styles.titleText}>{props?.text}</Text>
  );
  const listHeader = (
    <>
      {props.titleControl}
      {props.text ? headerText : null}
      {choices.length > 10 ? (
        <SearchBar
          autoFocus={false}
          value={searchText}
          isCloseVisible={isSearching}
          onFocus={handleSearchTextFocus}
          onClose={handleSearchTextClose}
          onChangeText={handleSearchTextChange}
        />
      ) : null}
    </>
  );
  return (
    <KeyboardFlatList
      contentContainerStyle={isFishMeet ? fmStyles.screenContainer : styles.screenContainer}
      ListHeaderComponent={listHeader}
      ListHeaderComponentStyle={{ alignItems: 'center' }}
      keyExtractor={(item, index) => item[props?.keyString || 'groupId'] ?? index++}
      data={showChoices}
      renderItem={({ item, index }) => {
        const isCurrent = typeof props.isCurrent === 'function' && props.isCurrent(item);
        return typeof props.onRenderItem === 'function' ? (
          isFishMeet ? (
            <RectButton
              style={{ marginBottom: 2 }}
              key={item?.[props?.keyString || 'groupId'] ?? index++}
              onPress={() => {
                props.onSelect(item);
              }}>
              {props.onRenderItem(item)}
            </RectButton>
          ) : (
            <TouchableOpacity
              style={{ marginBottom: 2 }}
              activeOpacity={1}
              key={item?.[props?.keyString || 'groupId'] ?? index++}
              onPress={() => {
                props.onSelect(item);
              }}>
              {props.onRenderItem(item)}
            </TouchableOpacity>
          )
        ) : (
          <DefaultSelectItem
            key={item?.[props?.keyString || 'groupId'] ?? index++}
            id={item?.[props?.keyString || 'groupId']}
            icon={props?.itemIcon?.(item)}
            onSelect={() => {
              props.onSelect(item);
            }}
            checked={isCurrent}
            multiple={!!props.multiple}
            name={props.getDisplayName(item)}
          />
        );
      }}
    />
  );
};

export default Select;

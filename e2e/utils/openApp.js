/* eslint-disable no-undef */
const appConfig = require('app.json');
const { resolveConfig } = require('detox/internals');

const platform = device.getPlatform();

module.exports.openApp = async function openApp() {
  const config = await resolveConfig();
  console.log('config is: ' + config.configurationName);
  if (config.configurationName.split('.')[2] === 'debug') {
    return await openAppForDebugBuild(platform);
  } else {
    return await device.launchApp({
      newInstance: true
    });
  }
};

async function openAppForDebugBuild(platform) {
  const deepLinkUrl = process.env.EXPO_USE_UPDATES
    ? // Testing latest published EAS update for the test_debug channel
      getDeepLinkUrl(getLatestUpdateUrl())
    : // Local testing with packager
      getDeepLinkUrl(getDevLauncherPackagerUrl(platform));

  console.log(deepLinkUrl);
  if (platform === 'ios') {
    await device.launchApp({
      newInstance: true
    });
    sleep(3000);
    await device.openURL({
      url: deepLinkUrl
    });
  } else {
    await device.launchApp({
      newInstance: true,
      url: deepLinkUrl
    });
  }

  await sleep(3000);
}

const getDeepLinkUrl = (url) => `exp+idigest://expo-development-client/?url=${encodeURIComponent(url)}`;

function getDevLauncherPackagerUrl(platform) {
  if (platform === 'ios') {
    return 'http://localhost:8081?disableOnboarding=1';
  } else {
    //Android avd can only see ********, but not localhost
    return 'http://********:8081?disableOnboarding=1';
  }
}

const getLatestUpdateUrl = () => `https://u.expo.dev/${getAppId()}?channel-name=test_debug&disableOnboarding=1`;

const getAppId = () => appConfig?.expo?.extra?.eas?.projectId ?? '';

const sleep = (t) => new Promise((res) => setTimeout(res, t));

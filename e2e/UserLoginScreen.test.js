/* eslint-disable no-undef */
const { openApp } = require('e2e/utils/openApp');

describe('UserLoginScreen', () => {
  beforeAll(async () => {
    await openApp();
  });

  beforeEach(async () => {
    // add 3sec timeout so that the UI will fully render on Android for the test to pass
    await new Promise((r) => setTimeout(r, 3000));
  });

  it('should have the login button', async () => {
    await expect(element(by.id('LoginButton'))).toBeVisible();
  });

  it('continue to show login screen when tap login button without email', async () => {
    await element(by.id('LoginButton')).tap();
    await expect(element(by.id('EmailInputBox'))).toBeVisible();
  });
});
